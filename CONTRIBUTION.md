## Contribution

This is great that you'd like to contribute to this project. All change requests should go through the steps described below.

## Pull Requests

**Please, make sure you open an issue before starting with a Pull Request, unless it's a typo or a really obvious error.** Pull requests are the best way to propose changes.

## Conventional commits

Our repository follow [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/#summary) specification. Releasing to GitHub and NuGet is done with the support of [semantic-release](https://semantic-release.gitbook.io/semantic-release/).

Pull requests should have a title that follows the specification, otherwise, merging is blocked. If you are not familiar with the specification simply ask maintainers to modify. You can also use this cheatsheet if you want:

Pull requests should have a title that follows the specification, otherwise, merging is blocked. If you are not familiar with the specification simply ask maintainers to modify. You can also use this cheatsheet if you want:

- `build: ` prefix in the title indicates that PR contains changes to the build system/dependencies and there is no need to trigger release.
- `chore: ` prefix in the title indicates that PR is only related to clean up in the project and there is no need to trigger release.
- `ci: ` prefix in the title indicates that PR contains changes to CI configuration files/scripts and there is no need to trigger release.
- `docs: ` prefix in the title indicates that PR is only related to the documentation and there is no need to trigger release.
- `feat: ` prefix in the title indicates that PR is a feature and MINOR release must be triggered.
- `fix: ` prefix in the title indicates that PR is a bug fix and PATCH release must be triggered.
- `perf: ` prefix in the title indicates that PR contains performance improvements and PATCH release must be triggered.
- `refactor: ` prefix in the title indicates that PR is only related to refactoring and there is no need to trigger release.
- `revert: ` prefix in the title indicates that PR reverts a previous commit and there is no need to trigger release.
- `style: ` prefix in the title indicates that PR contains formatting/whitespace changes and there is no need to trigger release.
- `test: ` prefix in the title indicates that PR is only related to tests and there is no need to trigger release.

> What about MAJOR release? just add `!` to the prefix, like `fix!: ` or `refactor!: `


## Commit message

**# Breaking Changes**

💥 Breaking [component/system name] for [reason for breaking change]

**# New Feature**

✨ Add [feature name] for [purpose/benefit]

**# New Small Feature**

🧩 Add [minor feature name] for [specific improvement]

**# Feature Enhancement**

🌟 Enhance [existing feature] by [enhancement details]

**# Improving Feature**

🧱 Improve [component/system] by [improvement method]

**# Minor Tweaks**

🔧 Tweak [component] for [environment/context]

**# Refactoring**

🔨 Refactor [module/component] for [benefit gained]

**# Performance Improvement**

🚀 Improve [performance area] by [optimization method]

**# Hotfix**

🚑 Fix [urgent issue] for [affected system]

**# Work In Progress**

🚧 WIP [feature/component] for [target system]

**# Task Completion**

✅ Complete [project/task] for [milestone/release]

**# Typo Fix**

📝 Fix [typo location] in [file/document]

**# Code Review Changes**

👀 Update [code area] for [review feedback]

**# Configuration Changes**

🔧 Update [config type] for [environment]

**# Bug Fix**

🐛 Fix [bug description] for [affected feature]

**# Removal**

📝️ Remove [deprecated component] for [reason]

**# Documentation**

📚 Update [doc type] for [subject]

**# Style and Formatting**

💄 Update [styling element] with [tool/method]

**# Testing**

🧪 Add [test type] for [component/feature]

**# Dependency Updates**

📈 Update [package] to [version]

📉 Downgrade [package] to [version]

**# Deployment/CI**

🚀 Deploy [service] to [environment]

**# Docker**

🐳 Update [docker component] for [purpose]

**# Accessibility**

🦾 Improve [accessibility feature] for [component]

**# Internationalization**

🌐 Add [language/region] for [component]

**# Code Enforcement**

🚔 Enforce [rule set] in [scope]

**# Security**

🔒 Improve [security aspect] for [component]

**# Monitoring**

🔎 Add [monitoring type] for [purpose]

**# Reliability**

🛡️ Enhance [system component] by [method]

**# Examples**

📌 Add [example type] for [purpose]

**# Analytics**

📊 Add [tracking type] for [metric]

**# UI/UX**

🎨 Enhance [UI element] for [improvement]

**# Future Work**

📅 Add [task type] for [future goal]


## Code Formatting

- Use `dotnet format` or `dotnet format --verbosity diagnostic` to format the code.
- Use `dotnet format --verify-no-changes` to verify the code formatting.
- Use `dotnet csharpier .` to format the code style.


## Resources

- [How to Contribute to Open Source](https://opensource.guide/how-to-contribute/)
- [Using Pull Requests](https://help.github.com/articles/about-pull-requests/)
- [GitHub Help](https://help.github.com)
