using BuildingBlocks.DDD.SeedWork.Primitives;
using BuildingBlocks.Persistence.Extensions;
using Subscription.Domain.Aggregates.PriceListItemAggregate.Enumerations;
using Subscription.Domain.Aggregates.SubscriptionAggregate.Enumerations;
using Subscription.Domain.Aggregates.SubscriptionPaymentAggregate.Enumerations;

namespace Subscription.Persistence.Persistence;

/// <summary>
/// Seed database with initial data for <see cref="SubscriptionContext"/>.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="SubscriptionContextSeed"/> class.
/// </remarks>
/// <param name="subscriptionContext">The subscription context.</param>
public sealed class SubscriptionContextSeed(SubscriptionContext subscriptionContext) : IDbSeeder
{
    /// <inheritdoc />
    public Task SeedAsync(CancellationToken cancellationToken = default)
    {
        if (!subscriptionContext.PriceListItemCategories.Any())
        {
            subscriptionContext.PriceListItemCategories.AddRange(Enumeration.GetAll<PriceListItemCategory>());
        }

        if (!subscriptionContext.SubscriptionPeriods.Any())
        {
            subscriptionContext.SubscriptionPeriods.AddRange(Enumeration.GetAll<SubscriptionPeriod>());
        }

        if (!subscriptionContext.SubscriptionPaymentStatuses.Any())
        {
            subscriptionContext.SubscriptionPaymentStatuses.AddRange(Enumeration.GetAll<SubscriptionPaymentStatus>());
        }

        return subscriptionContext.SaveChangesAsync(cancellationToken);
    }
}
