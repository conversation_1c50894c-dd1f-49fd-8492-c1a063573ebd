using AutoMapper;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.CQRS.Commands;
using BuildingBlocks.Security.Jwt.Services;
using MainArchitecture.Domain.SharedKernel.ValueObjects;
using Subscription.Application.Features.PriceListItem.Factories;
using Subscription.Domain.Aggregates.SubscriptionPaymentAggregate.Data;
using Subscription.Domain.Aggregates.SubscriptionPaymentAggregate.Services;
using SubscriptionPaymentDomain = Subscription.Domain.Aggregates.SubscriptionPaymentAggregate.Entities.SubscriptionPayment;

namespace Subscription.Application.Features.SubscriptionPayment.Commands.BuySubscription;

/// <summary>
/// Handles the processing of the <see cref="BuySubscriptionCommand"/> command.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="BuySubscriptionCommandHandler"/> class.
/// </remarks>
/// <param name="priceListItemsDatabaseQuery">The price list items database query.</param>
/// <param name="subscriptionPaymentRepository">The repository for subscription payments.</param>
/// <param name="activeSubscriptionCheckerService">The active subscription checker.</param>
/// <param name="userAccessor">The user accessor for accessing user information.</param>
/// <param name="mapper">The mapper.</param>
internal sealed class BuySubscriptionCommandHandler(
    IPriceListItemsDatabaseQuery priceListItemsDatabaseQuery,
    ISubscriptionPaymentRepository subscriptionPaymentRepository,
    IActiveSubscriptionCheckerService activeSubscriptionCheckerService,
    IUserAccessor userAccessor,
    IMapper mapper
) : ICommandHandler<BuySubscriptionCommand, Guid>
{
    /// <inheritdoc />
    public async Task<Result<Guid>> Handle(BuySubscriptionCommand request, CancellationToken cancellationToken)
    {
        var priceList = await PriceListFactory.CreatePriceList(
            priceListItemsDatabaseQuery,
            discountPrice: null,
            cancellationToken
        );
        var activeSubscriptionsCount = await activeSubscriptionCheckerService.ActiveSubscriptionCountAsync(
            userAccessor.GetUserIdentity,
            cancellationToken
        );

        var subscriptionPayment = SubscriptionPaymentDomain.Create(
            userId: userAccessor.GetUserIdentity,
            email: userAccessor.GetUserEmail,
            subscriptionPeriodId: request.SubscriptionPeriodId,
            priceOffer: mapper.Map<MoneyValue>(request.Price),
            priceList: priceList,
            activeSubscriptionsCount: activeSubscriptionsCount
        );

        subscriptionPaymentRepository.Create(subscriptionPayment);
        await subscriptionPaymentRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);

        return subscriptionPayment.Id.Value;
    }
}
