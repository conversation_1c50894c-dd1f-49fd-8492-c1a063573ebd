using BuildingBlocks.Core.Resources;
using BuildingBlocks.Core.Types.Extensions;
using BuildingBlocks.Validation.Extensions;
using FluentValidation;
using Microsoft.Extensions.Logging;
using Subscription.Domain.Resources;

namespace Subscription.Application.Features.SubscriptionPayment.Commands.SetSubscriptionOfPayment;

/// <summary>
/// Validates the <see cref="SetSubscriptionOfPaymentCommand"/> before it's processed by the command handler <see cref="SetSubscriptionOfPaymentCommandHandler"/>.
/// </summary>
internal sealed class SetSubscriptionOfPaymentCommandValidator : AbstractValidator<SetSubscriptionOfPaymentCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="SetSubscriptionOfPaymentCommandValidator"/> class.
    /// </summary>
    /// <param name="logger">The logger.</param>
    public SetSubscriptionOfPaymentCommandValidator(ILogger<SetSubscriptionOfPaymentCommandValidator> logger)
    {
        RuleFor(r => r.SubscriptionPaymentId)
            .ValidateGuid(SharedResource.Entity_Id.FormatWithStr(DomainResource.Subscription_Payment));

        RuleFor(r => r.SubscriptionId)
            .ValidateGuid(SharedResource.Entity_Id.FormatWithStr(DomainResource.Subscription));

        if (logger.IsEnabled(LogLevel.Trace))
        {
            logger.LogTrace("INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}
