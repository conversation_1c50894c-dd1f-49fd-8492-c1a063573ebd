using BuildingBlocks.Core.Resources;
using BuildingBlocks.Validation.Extensions;
using FluentValidation;
using MainArchitecture.Domain.SharedKernel.Enumerations;
using Microsoft.Extensions.Logging;

namespace Subscription.Application.Features.SubscriptionPayment.Commands.StartFreeTrialSubscription;

/// <summary>
/// Validates the <see cref="StartFreeTrialSubscriptionCommand"/> before it's processed by the command handler <see cref="StartFreeTrialSubscriptionCommandHandler"/>.
/// </summary>
internal sealed class StartFreeTrialSubscriptionCommandValidator : AbstractValidator<StartFreeTrialSubscriptionCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="StartFreeTrialSubscriptionCommandValidator"/> class.
    /// </summary>
    /// <param name="logger">The logger.</param>
    public StartFreeTrialSubscriptionCommandValidator(ILogger<StartFreeTrialSubscriptionCommandValidator> logger)
    {
        RuleFor(r => r.Country)
            .MustBeValidEnumeration<StartFreeTrialSubscriptionCommand, Country>(SharedResource.Country);

        if (logger.IsEnabled(LogLevel.Trace))
        {
            logger.LogTrace("INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}
