using BuildingBlocks.Core.Exception;
using BuildingBlocks.Core.IdGenerator;
using BuildingBlocks.DDD.SeedWork.Primitives;
using MainArchitecture.Domain.SeedWork.Abstractions;
using MainArchitecture.Domain.SharedKernel.ValueObjects;
using Subscription.Domain.Aggregates.PriceListItemAggregate.Enumerations;
using Subscription.Domain.Aggregates.PriceListItemAggregate.ValueObjects;
using Subscription.Domain.Aggregates.SubscriptionAggregate.Enumerations;

namespace Subscription.Domain.Aggregates.PriceListItemAggregate.Entities;

/// <summary>
/// Represents the PriceListItem entity in the domain.
/// </summary>
public sealed class PriceListItem : AggregateRoot<PriceListItemId>, IActive
{
    #region Constructor

    /// <summary>
    /// Initializes a new instance of the <see cref="PriceListItem"/> class.
    /// </summary>
    private PriceListItem()
    {
        Price = default!;
        IsActive = true;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="PriceListItem"/> class.
    /// </summary>
    /// <param name="subscriptionPeriodId">The subscription period ID.</param>
    /// <param name="priceListItemCategoryId">The price list item category ID.</param>
    /// <param name="price">The price.</param>
    private PriceListItem(int subscriptionPeriodId, int priceListItemCategoryId, MoneyValue price)
        : this()
    {
        Id = new PriceListItemId(IdGenerator.NewId());
        Price = price;
        _subscriptionPeriodId = subscriptionPeriodId;
        _priceListItemCategoryId = priceListItemCategoryId;
    }

    #endregion

    #region Properties

    /// <summary>
    /// Gets the money value.
    /// </summary>
    public MoneyValue Price { get; private set; }

    /// <inheritdoc/>
    public bool IsActive { get; private set; }

    /// <summary>
    /// Gets the subscription period.
    /// </summary>
    public SubscriptionPeriod? SubscriptionPeriod { get; private set; }

    /// <summary>
    /// Gets or sets the subscription period ID.
    /// </summary>
    private int _subscriptionPeriodId;

    /// <summary>
    /// Gets the price list item category.
    /// </summary>
    public PriceListItemCategory? PriceListItemCategory { get; private set; }

    /// <summary>
    /// Gets or sets the price list item category ID.
    /// </summary>
    private int _priceListItemCategoryId;

    #endregion

    #region Methods

    #region Factory Methods

    /// <summary>
    /// Creates a new instance of the <see cref="PriceListItem"/> class.
    /// </summary>
    /// <param name="subscriptionPeriodId">The subscription period ID.</param>
    /// <param name="priceListItemCategoryId">The price list item category ID.</param>
    /// <param name="price">The price.</param>
    /// <returns>The created <see cref="PriceListItem"/> instance.</returns>
    public static PriceListItem Create(int subscriptionPeriodId, int priceListItemCategoryId, MoneyValue price)
    {
        Ensure.NotNull(price, nameof(price));
        Ensure.NotZeroOrLess(subscriptionPeriodId, nameof(subscriptionPeriodId));
        Ensure.NotZeroOrLess(priceListItemCategoryId, nameof(priceListItemCategoryId));
        return new PriceListItem(subscriptionPeriodId, priceListItemCategoryId, price);
    }

    #endregion

    /// <summary>
    /// Changes the attributes of the price list item.
    /// </summary>
    /// <param name="subscriptionPeriodId">The subscription period ID.</param>
    /// <param name="priceListItemCategoryId">The price list item category ID.</param>
    /// <param name="price">The price.</param>
    public void ChangeAttributes(int subscriptionPeriodId, int priceListItemCategoryId, MoneyValue price)
    {
        Price = Ensure.NotNull(price, nameof(price));
        _subscriptionPeriodId = Ensure.NotZeroOrLess(subscriptionPeriodId, nameof(subscriptionPeriodId));
        _priceListItemCategoryId = Ensure.NotZeroOrLess(priceListItemCategoryId, nameof(priceListItemCategoryId));
    }

    /// <summary>
    /// Changes the activation status.
    /// </summary>
    public void ChangeActivation()
    {
        IsActive = !IsActive;
    }

    /// <summary>
    /// Gets the subscription period.
    /// </summary>
    public int GetSubscriptionPeriodId()
    {
        return _subscriptionPeriodId;
    }

    /// <summary>
    /// Gets the price list item category.
    /// </summary>
    public int GetPriceListItemCategoryId()
    {
        return _priceListItemCategoryId;
    }

    #endregion
}
