using BuildingBlocks.DDD.SeedWork.Primitives;
using Subscription.Domain.Resources;

namespace Subscription.Domain.Aggregates.SubscriptionPaymentAggregate.Rules;

/// <summary>
/// Represents a business rule that checks if a user used a free trial before.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="UserMustNotHadSubscriptionBeforeRule"/> class.
/// </remarks>
/// <param name="subscriptionsBeforeCount">The active subscription before count.</param>
internal sealed class UserMustNotHadSubscriptionBeforeRule(int subscriptionsBeforeCount) : IBusinessRule
{
    /// <inheritdoc />
    public bool IsBroken() => subscriptionsBeforeCount > 0;

    /// <inheritdoc />
    public string Message => DomainResource.Already_Had_Active_Or_Free_Trial_Error_Message;
}
