using BuildingBlocks.Web.Constants;
using SubscriptionRoutes = Subscription.API.Configurations.Constants.Routes;

namespace Subscription.EndToEndTests;

/// <summary>
/// Represents the constants for the end-to-end tests.
/// </summary>
public static class Constants
{
    /// <summary>
    /// Represents the base route for the API.
    /// </summary>
    public static class Routes
    {
        /// <summary>
        /// Represents the base route for the API.
        /// </summary>
        private static readonly string _baseAddress =
            $"{WebConstants.Route.ApiPrefix}/v{WebConstants.Route.ApiMajorVersion}";

        /// <summary>
        /// Represents the route for the admin price list items APIS.
        /// </summary>
        public static class AdminPriceList
        {
            /// <summary>
            /// Represents the route for the add price list item API.
            /// </summary>
            public static readonly string _mainUrl = $"{_baseAddress}/{SubscriptionRoutes.AdminPriceList.MainUrl}";

            /// <summary>
            /// Represents the route for the add price list item API.
            /// </summary>
            public static readonly string AddPriceList = $"{_mainUrl}/{SubscriptionRoutes.AdminPriceList.AddPriceList}";

            /// <summary>
            /// Represents the route for the update price list item API.
            /// </summary>
            public static readonly string UpdatePriceList =
                $"{_mainUrl}/{SubscriptionRoutes.AdminPriceList.UpdatePriceList}";

            /// <summary>
            /// Represents the route for the change price list item activation API.
            /// </summary>
            /// <param name="priceListItemId">The price list item ID.</param>
            public static string ChangePriceListActivation(Guid priceListItemId) =>
                $"{
                _mainUrl
            }/change-activation/{
                priceListItemId
            }";
        }

        /// <summary>
        /// Represents the route for the price list items APIS.
        /// </summary>
        public static class PriceList
        {
            /// <summary>
            /// Represents the route for the get filtered price list items API.
            /// </summary>
            public static readonly string _mainUrl = $"{_baseAddress}/{SubscriptionRoutes.PriceList.MainUrl}";

            /// <summary>
            /// Represents the route for the get filtered price list items API.
            /// </summary>
            public static readonly string GetFilteredPriceLists =
                $"{_mainUrl}/{SubscriptionRoutes.PriceList.GetFilteredPriceLists}";

            /// <summary>
            /// Represents the route for the get price list item by ID API.
            /// </summary>
            /// <param name="priceListItemId">The price list item ID.</param>
            public static string GetPriceListById(Guid priceListItemId) => $"{_mainUrl}/{priceListItemId}";
        }

        /// <summary>
        /// Represents the route for the subscription payment APIS.
        /// </summary>
        public static class SubscriptionPayment
        {
            /// <summary>
            /// Represents the route for the buy subscription API.
            /// </summary>
            public static readonly string _mainUrl = $"{_baseAddress}/{SubscriptionRoutes.SubscriptionPayment.MainUrl}";

            /// <summary>
            /// Represents the route for the buy subscription API.
            /// </summary>
            public static readonly string StartFreeTrialSubscription = $"{_mainUrl}/start-trial";

            /// <summary>
            /// Represents the route for the buy subscription API.
            /// </summary>
            public static readonly string BuySubscription = $"{_mainUrl}/buy";

            /// <summary>
            /// Represents the route for the renew subscription API.
            /// </summary>
            public static readonly string BuySubscriptionRenewal = $"{_mainUrl}/renew";
        }
    }
}
