using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Validation.Common;
using FluentAssertions;
using MainArchitecture.IntegrationEvents.Events.Subscription;
using Subscription.API;
using Subscription.Application.Features.Subscription.Commands.CreateFreeTrialSubscription;
using Subscription.Domain.Aggregates.SubscriptionAggregate.Enumerations;
using Subscription.Domain.Aggregates.SubscriptionAggregate.Events;
using Subscription.IntegrationTests.Abstractions;
using Subscription.Persistence.Persistence;
using Subscription.TestShared.Fakes.Subscription.Requests;
using Subscription.TestShared.Helpers;
using Subscription.TestShared.Helpers.Probs;
using Xunit.Abstractions;

namespace Subscription.IntegrationTests.Features.Subscription.Commands.CreateFreeTrialSubscription;

/// <summary>
/// Represents the tests for the <see cref="CreateFreeTrialSubscriptionCommand"/> command.
/// </summary>
public class CreateFreeTrialSubscriptionTests : SubscriptionIntegrationTestBase
{
    #region Constructor

    private readonly CreateFreeTrialSubscriptionCommand _command;

    /// <summary>
    /// Initializes a new instance of the <see cref="CreateFreeTrialSubscriptionTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public CreateFreeTrialSubscriptionTests(
        SharedFixtureWithEfCore<Program, SubscriptionContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        _command = new FakeCreateFreeTrialSubscriptionCommand().Generate();
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenAllConditionsAreMet_ShouldCreateFreeTrialSubscription()
    {
        // Arrange
        var command = await ArrangeCommandAsync();

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertSuccessResult();
        response.Value.Should().BeTrue();

        var insertedSubscription = await SubscriptionHelpers.GetSubscriptionByUserIdAsync(
            SharedFixture,
            command.UserId,
            CancellationToken
        );
        insertedSubscription.Should().NotBeNull();
        insertedSubscription!.GetSubscriptionPeriodId().Should().Be(SubscriptionPeriod.FreeTrial);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public Task Send_WhenSubscriptionIsNotValid_ShouldThrowValidationException()
    {
        // Arrange
        var command = new FakeInvalidCreateFreeTrialSubscriptionCommand().Generate();

        // Act & Assert
        return FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<ValidationException>();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSubscriptionCreated_ShouldPersistInTheOutbox()
    {
        // Arrange
        var command = await ArrangeCommandAsync();

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertSuccessResult();

        await SharedFixture.ShouldProcessedOutboxPersistMessage<SubscriptionCreatedDomainEvent>(
            cancellationToken: CancellationToken
        );
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSubscriptionFreeTrialCreated_ShouldPersistInTheOutbox()
    {
        // Arrange
        var command = await ArrangeCommandAsync();

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertSuccessResult();

        await SharedFixture.ShouldProcessedOutboxPersistMessage<FreeTrialSubscriptionStartedDomainEvent>(
            cancellationToken: CancellationToken
        );
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSubscriptionCreated_ShouldPublishIntegrationEventToTheBroker()
    {
        // Arrange
        var command = await ArrangeCommandAsync();

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertSuccessResult();

        await SharedFixture.WaitForPublishing<FreeTrialSubscriptionStartedIntegrationEvent>(
            cancellationToken: CancellationToken
        );
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSubscriptionCreated_ShouldSetSubscriptionOfPayment()
    {
        // Arrange
        var command = await ArrangeCommandAsync();

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertSuccessResult();

        var probe = new GetSubscriptionOfSubscriptionPaymentByIdAndUserProb(
            SharedFixture,
            command.SubscriptionPaymentId,
            command.UserId,
            CancellationToken
        );

        await SharedFixture.AssertEventually(probe, cancellationToken: CancellationToken);
    }

    /// <summary>
    /// Inserts a subscription payment.
    /// </summary>
    /// <returns>The created <see cref="CreateFreeTrialSubscriptionCommand"/> instance.</returns>
    private async Task<CreateFreeTrialSubscriptionCommand> ArrangeCommandAsync()
    {
        var subscriptionPayment = await SubscriptionPaymentHelpers.InsertSubscriptionPaymentAsync(
            SharedFixture,
            isFreeTrial: true,
            e2e: false,
            userId: SharedFixture.Faker.Random.Guid(),
            email: SharedFixture.Faker.Internet.Email(),
            cancellationToken: CancellationToken
        );

        return new CreateFreeTrialSubscriptionCommand(
            subscriptionPayment.Id,
            subscriptionPayment.UserId,
            subscriptionPayment.Price.Currency.Country
        );
    }
}
