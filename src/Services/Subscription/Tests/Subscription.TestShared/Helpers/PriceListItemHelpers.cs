using Ardalis.Specification.EntityFrameworkCore;
using BuildingBlocks.DDD.SeedWork.Primitives;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using MainArchitecture.Domain.SharedKernel.Enumerations;
using MainArchitecture.Domain.SharedKernel.ValueObjects;
using MainArchitecture.TestShared.Fakes.Common.Enumerations;
using Microsoft.EntityFrameworkCore;
using Subscription.API;
using Subscription.Domain.Aggregates.PriceListItemAggregate.Enumerations;
using Subscription.Domain.Aggregates.PriceListItemAggregate.Snapshots;
using Subscription.Domain.Aggregates.PriceListItemAggregate.Specifications;
using Subscription.Domain.Aggregates.PriceListItemAggregate.ValueObjects;
using Subscription.Domain.Aggregates.SubscriptionAggregate.Enumerations;
using Subscription.Persistence.Persistence;
using Subscription.TestShared.Fakes.PriceListItem.Entities;
using Subscription.TestShared.Fakes.PriceListItem.Snapshots;
using PriceListDomain = Subscription.Domain.Aggregates.PriceListItemAggregate.Entities.PriceListItem;

namespace Subscription.TestShared.Helpers;

/// <summary>
/// Represents a collection of price list integration test helpers.
/// </summary>
public static class PriceListItemHelpers
{
    /// <summary>
    /// Inserts a price list into the database.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="isFreeTrial">That contains a free trial.</param>
    /// <param name="priceListItemCategory">The price list item category.</param>
    /// <param name="subscriptionPeriod">The subscription period.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The inserted price list.</returns>
    public static async Task<PriceListDomain> InsertPriceListAsync(
        SharedFixtureWithEfCore<Program, SubscriptionContext> sharedFixture,
        bool isFreeTrial = false,
        PriceListItemCategory? priceListItemCategory = null,
        SubscriptionPeriod? subscriptionPeriod = null,
        CancellationToken cancellationToken = default
    )
    {
        if (isFreeTrial)
        {
            var freePackage = new FakePriceListItem()
                .WithSubscriptionPeriodId(SubscriptionPeriod.FreeTrial)
                .WithPriceListItemCategory(PriceListItemCategory.New)
                .WithPrice(MoneyValue.Zero(new FakeEnumeration<Currency>().Generate()))
                .Generate();
            await sharedFixture.InsertEfDbContextAsync(freePackage, cancellationToken);
            return freePackage;
        }

        var priceList = new FakePriceListItem()
            .WithSubscriptionPeriodId(
                subscriptionPeriod
                    ?? new FakeEnumeration<SubscriptionPeriod>(excludedItems: SubscriptionPeriod.FreeTrial).Generate()
            )
            .WithPriceListItemCategory(priceListItemCategory ?? PriceListItemCategory.New)
            .Generate();
        await sharedFixture.InsertEfDbContextAsync(priceList, cancellationToken);
        return priceList;
    }

    /// <summary>
    /// Inserts a price list into the database.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="totalItems">The total items.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The inserted price list.</returns>
    public static async Task<List<PriceListDomain>> InsertPriceListItemsAsync(
        SharedFixtureWithEfCore<Program, SubscriptionContext> sharedFixture,
        int totalItems,
        CancellationToken cancellationToken
    )
    {
        var insertedCombinations = new HashSet<(int SubscriptionPeriodId, int PriceListItemCategoryId)>();
        var generatedPrices = new List<PriceListDomain>();
        var faker = new FakePriceListItem();

        while (generatedPrices.Count < totalItems)
        {
            var priceListItem = faker.Generate();
            var combination = (priceListItem.GetSubscriptionPeriodId(), priceListItem.GetPriceListItemCategoryId());

            if (insertedCombinations.Add(combination))
            {
                generatedPrices.Add(priceListItem);
            }

            if (insertedCombinations.Count >= totalItems && generatedPrices.Count < totalItems)
            {
                throw new InvalidOperationException("Not enough unique combinations available.");
            }
        }

        await sharedFixture.InsertEfDbContextAsync(generatedPrices.ToArray(), cancellationToken);
        return generatedPrices;
    }

    /// <summary>
    /// Gets a snapshot from a price list item.
    /// </summary>
    /// <param name="priceListItem">The price list item.</param>
    /// <returns>The snapshot.</returns>
    public static PriceListItemDataSnapshot GetSnapshotFromPriceListItem(PriceListDomain priceListItem)
    {
        var period = Enumeration.FromValue<SubscriptionPeriod>(priceListItem.GetSubscriptionPeriodId());
        var category = Enumeration.FromValue<PriceListItemCategory>(priceListItem.GetPriceListItemCategoryId());
        var priceListSnapshot = new FakePriceListItemDataSnapshot()
            .WithSubscriptionPeriod(period)
            .WithPrice(priceListItem.Price)
            .WithPriceListItemCategory(category)
            .Generate();
        return priceListSnapshot;
    }

    /// <summary>
    /// Gets a price list by ID.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="id">The price list identifier.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The price list.</returns>
    public static Task<PriceListDomain?> GetPriceListByIdAsync(
        SharedFixtureWithEfCore<Program, SubscriptionContext> sharedFixture,
        PriceListItemId id,
        CancellationToken cancellationToken = default
    )
    {
        var priceList = sharedFixture.ExecuteEfDbContextAsync(db =>
            db.PriceListItems.WithSpecification(new PriceListItemByIdSpec(id, isReadOnly: true))
                .FirstOrDefaultAsync(cancellationToken)
        );
        return priceList;
    }

    /// <summary>
    /// Gets price list items.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The price list items.</returns>
    public static Task<List<PriceListDomain>> GetPriceListItemsAsync(
        SharedFixtureWithEfCore<Program, SubscriptionContext> sharedFixture,
        CancellationToken cancellationToken = default
    )
    {
        return sharedFixture.ExecuteEfDbContextAsync(db => db.PriceListItems.ToListAsync(cancellationToken));
    }
}
