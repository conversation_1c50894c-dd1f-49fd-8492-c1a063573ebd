using Ardalis.Specification.EntityFrameworkCore;
using BuildingBlocks.Core.Common;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using Dapper;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds;
using Microsoft.EntityFrameworkCore;
using Subscription.API;
using Subscription.Domain.Aggregates.PriceListItemAggregate.Entities;
using Subscription.Domain.Aggregates.PriceListItemAggregate.Snapshots;
using Subscription.Domain.Aggregates.SubscriptionPaymentAggregate.Entities;
using Subscription.Domain.Aggregates.SubscriptionPaymentAggregate.Enumerations;
using Subscription.Domain.Aggregates.SubscriptionPaymentAggregate.Specifications;
using Subscription.Domain.Aggregates.SubscriptionPaymentAggregate.ValueObjects;
using Subscription.Domain.Constants;
using Subscription.Persistence.Persistence;
using SubscriptionPaymentDomain = Subscription.Domain.Aggregates.SubscriptionPaymentAggregate.Entities.SubscriptionPayment;

namespace Subscription.TestShared.Helpers;

/// <summary>
/// Represents a helper class for the <see cref="SubscriptionPayment"/> class.
/// </summary>
public static class SubscriptionPaymentHelpers
{
    /// <summary>
    /// Inserts a subscription payment into the database.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="e2e">Indicates whether test is an e2e or not.</param>
    /// <param name="isFreeTrial">Indicates whether the subscription is free trial.</param>
    /// <param name="status">The subscription payment status.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="userId">The user identifier.</param>
    /// <param name="email">The email.</param>
    /// <param name="priceListItem">The price list item.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The inserted subscription payment.</returns>
    public static async Task<SubscriptionPaymentDomain> InsertSubscriptionPaymentAsync(
        SharedFixtureWithEfCore<Program, SubscriptionContext> sharedFixture,
        bool e2e = false,
        bool isFreeTrial = false,
        SubscriptionPaymentStatus? status = null,
        Guid? subscriptionId = null,
        Guid? userId = null,
        string? email = null,
        PriceListItem? priceListItem = null,
        CancellationToken cancellationToken = default
    )
    {
        status ??= SubscriptionPaymentStatus.WaitingForPayment;
        priceListItem ??= await PriceListItemHelpers.InsertPriceListAsync(
            sharedFixture,
            isFreeTrial,
            cancellationToken: cancellationToken
        );
        var snapshot = PriceListItemHelpers.GetSnapshotFromPriceListItem(priceListItem);

        userId ??= e2e ? sharedFixture.GetLoggedInUserId() : await sharedFixture.GetUserIdentityAsync();
        email ??= e2e ? sharedFixture.GetLoggedInUserEmail() : await sharedFixture.GetUserEmailAsync();
        if (status == SubscriptionPaymentStatus.Expired)
        {
            SystemClock.Set(
                DateTime.UtcNow.AddMinutes(-(DomainConstValues.PaymentSubscriptionExpirationMaxIntervalInMinute + 1))
            );
        }

        var subscriptionPayment = await InsertManualSubscriptionPaymentAsync(
            sharedFixture,
            snapshot,
            e2e,
            status,
            subscriptionId: subscriptionId,
            userId: userId,
            email: email,
            cancellationToken
        );

        SystemClock.Reset();
        return subscriptionPayment;
    }

    /// <summary>
    /// Starts a free trial subscription payment.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="snapshot">The price snapshot.</param>
    /// <param name="e2e">Indicates whether test is an e2e or not.</param>
    /// <param name="status">The subscription payment status.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="userId">The user identifier.</param>
    /// <param name="email">The email.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The inserted subscription payment.</returns>
    public static async Task<SubscriptionPaymentDomain> InsertManualSubscriptionPaymentAsync(
        SharedFixtureWithEfCore<Program, SubscriptionContext> sharedFixture,
        PriceListItemDataSnapshot snapshot,
        bool e2e = false,
        SubscriptionPaymentStatus? status = null,
        Guid? subscriptionId = null,
        Guid? userId = null,
        string? email = null,
        CancellationToken cancellationToken = default
    )
    {
        var user = userId ?? (e2e ? sharedFixture.GetLoggedInUserId() : await sharedFixture.GetUserIdentityAsync());
        var userEmail = email ?? (e2e ? sharedFixture.GetLoggedInUserEmail() : await sharedFixture.GetUserEmailAsync());
        var subscriptionPaymentId = sharedFixture.Faker.Random.Guid();
        status = subscriptionId is not null
            ? SubscriptionPaymentStatus.Paid
            : status ?? SubscriptionPaymentStatus.WaitingForPayment;
        const string sql = $"""
            INSERT INTO [subscription].[SubscriptionPayments] ([Id], [UserId], [SubscriptionId], [SubscriptionPeriodId], [SubscriptionPaymentStatusId], [Email], [MoneyValue_Currency], [MoneyValue_Value], [CreateDate], [Version])
            VALUES (@subscriptionPaymentId, @UserId, @SubscriptionId, @Period, @Status, @Email, @MoneyCurrency, @MoneyValue, GETDATE(), 0)

            """;
        await sharedFixture.ExecuteDapperCommandAsync(
            cn =>
                cn.ExecuteAsync(
                    sql,
                    new
                    {
                        subscriptionPaymentId,
                        UserId = user,
                        subscriptionId,
                        Period = snapshot.SubscriptionPeriod.Id,
                        Status = status.Id,
                        Email = userEmail,
                        MoneyCurrency = snapshot.Price.Currency.ShortCode,
                        MoneyValue = snapshot.Price.Value,
                    }
                ),
            cancellationToken
        );
        var payment = await GetSubscriptionPaymentByIdAndUserAsync(
            sharedFixture,
            subscriptionPaymentId,
            user,
            cancellationToken
        );
        return payment;
    }

    /// <summary>
    /// Gets a subscription payment by its identifier.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="subscriptionPaymentId">The subscription payment identifier.</param>
    /// <param name="userId">The user identifier.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The subscription payment.</returns>
    public static async Task<SubscriptionPaymentDomain> GetSubscriptionPaymentByIdAndUserAsync(
        SharedFixtureWithEfCore<Program, SubscriptionContext> sharedFixture,
        SubscriptionPaymentId subscriptionPaymentId,
        UserId userId,
        CancellationToken cancellationToken = default
    )
    {
        var subscriptionPayment = await sharedFixture.ExecuteEfDbContextAsync(db =>
            db.SubscriptionPayments.WithSpecification(
                    new SubscriptionPaymentByIdAndUserSpec(subscriptionPaymentId, userId, isReadOnly: true)
                )
                .FirstOrDefaultAsync(cancellationToken)
        );
        return subscriptionPayment;
    }

    /// <summary>
    /// Get SubscriptionPayments by user ID.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The subscription payment.</returns>
    public static Task<List<SubscriptionPaymentDomain>> GetExpiredSubscriptionPaymentsAsync(
        SharedFixtureWithEfCore<Program, SubscriptionContext> sharedFixture,
        CancellationToken cancellationToken = default
    )
    {
        return sharedFixture.ExecuteEfDbContextAsync(db =>
            db.SubscriptionPayments.Where(x => x.SubscriptionPaymentStatus == SubscriptionPaymentStatus.Expired)
                .ToListAsync(cancellationToken)
        );
    }
}
