using BuildingBlocks.Core.Exception.Types;
using BuildingBlocks.Core.IdGenerator;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Tests.UnitTests.Abstractions;
using FluentAssertions;
using Subscription.Domain.Aggregates.SubscriptionPaymentAggregate.ValueObjects;

namespace Subscription.UnitTests.SubscriptionPayment.ValueObjects;

/// <summary>
/// Represents a test class for the subscription payment identifier.
/// </summary>
public abstract class SubscriptionPaymentIdBaseTests : BaseDomainUnitTest
{
    #region Fields

    /// <summary>
    /// The subscription payment ID.
    /// </summary>
    protected readonly Guid FakeGuid = Faker.Random.Guid();

    #endregion
}

// summary>
// tests for <see cref="SubscriptionPaymentId"/> class.
// </summary>

public class SubscriptionPaymentIdTests : SubscriptionPaymentIdBaseTests
{
    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Create_WhenValidGuidProvided_ShouldReturnNewSubscriptionPaymentId()
    {
        // Arrange

        // Act
        var subscriptionPaymentId = SubscriptionPaymentId.Create(FakeGuid);

        // Assert
        subscriptionPaymentId.Should().NotBeNull();
        subscriptionPaymentId.Value.Should().Be(FakeGuid);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Create_WhenEmptyGuidProvided_ShouldThrowCustomAppException()
    {
        // Arrange
        var guid = IdGenerator.EmptyId;

        // Act & Assert
        FluentActions.Invoking(() => SubscriptionPaymentId.Create(guid)).Should().Throw<CustomAppException>();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Implicit_WhenConversionToGuid_ShouldReturnCorrectGuid()
    {
        // Arrange
        var subscriptionPaymentId = SubscriptionPaymentId.Create(FakeGuid);

        // Act
        Guid result = subscriptionPaymentId;

        // Assert
        result.Should().Be(FakeGuid);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Implicit_WhenConversionFromGuid_ShouldReturnCorrectSubscriptionPaymentId()
    {
        // Arrange

        // Act
        SubscriptionPaymentId subscriptionPaymentId = FakeGuid;

        // Assert
        subscriptionPaymentId.Should().NotBeNull();
        subscriptionPaymentId.Value.Should().Be(FakeGuid);
    }
}
