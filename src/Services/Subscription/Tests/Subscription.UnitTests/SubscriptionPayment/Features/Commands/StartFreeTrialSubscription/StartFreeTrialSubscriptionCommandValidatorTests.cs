using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using FluentValidation.TestHelper;
using Microsoft.Extensions.Logging.Abstractions;
using Subscription.Application.Features.SubscriptionPayment.Commands.StartFreeTrialSubscription;
using Subscription.TestShared.Fakes.SubscriptionPayment.Requests;

namespace Subscription.UnitTests.SubscriptionPayment.Features.Commands.StartFreeTrialSubscription;

/// <summary>
/// Represents the unit tests for the <see cref="StartFreeTrialSubscriptionCommandValidator"/> class.
/// </summary>
public class StartFreeTrialSubscriptionCommandValidatorTests
{
    #region Constructor

    private readonly StartFreeTrialSubscriptionCommandValidator _validator;

    /// <summary>
    /// Initializes a new instance of the <see cref="StartFreeTrialSubscriptionCommandValidatorTests"/> class.
    /// </summary>
    public StartFreeTrialSubscriptionCommandValidatorTests()
    {
        _validator = new StartFreeTrialSubscriptionCommandValidator(
            NullLogger<StartFreeTrialSubscriptionCommandValidator>.Instance
        );
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenValidInput_ShouldReturnTrue()
    {
        // Arrange
        var command = new FakeStartFreeTrialSubscriptionCommand().Generate();

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenInvalidInput_ShouldReturnFalse()
    {
        // Arrange
        var command = new FakeInvalidStartFreeTrialSubscriptionCommand().Generate();

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.ShouldHaveValidationErrorFor(x => x.Country);
    }
}
