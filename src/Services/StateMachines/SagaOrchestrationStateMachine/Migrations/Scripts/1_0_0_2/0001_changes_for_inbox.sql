BEGIN TRANSACTION;
ALTER TABLE [__MTOutboxMessages] ADD CONSTRAINT [FK___MTOutboxMessages___MTInboxStates_InboxMessageId_InboxConsumerId] FOREIGN KEY ([InboxMessageId], [InboxConsumerId]) REFERENCES [__MTInboxStates] ([MessageId], [ConsumerId]);

ALTER TABLE [__MTOutboxMessages] ADD CONSTRAINT [FK___MTOutboxMessages___MTOutboxStates_OutboxId] FOREIGN KEY ([OutboxId]) REFERENCES [__MTOutboxStates] ([OutboxId]);

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20241124101019_ChangesForInbox', N'9.0.0');

COMMIT;
GO

