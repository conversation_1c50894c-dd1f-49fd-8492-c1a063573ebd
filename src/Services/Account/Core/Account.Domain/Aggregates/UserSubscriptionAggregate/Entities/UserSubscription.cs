using Account.Domain.Aggregates.UserSubscriptionAggregate.Enumerations;
using Account.Domain.Aggregates.UserSubscriptionAggregate.Events;
using Account.Domain.Aggregates.UserSubscriptionAggregate.ValueObjects;
using BuildingBlocks.Core.Common;
using BuildingBlocks.Core.Exception;
using BuildingBlocks.DDD.SeedWork.Primitives;
using MainArchitecture.Domain.SharedKernel.Rules;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds;

namespace Account.Domain.Aggregates.UserSubscriptionAggregate.Entities;

/// <summary>
/// Represents a user subscription entity within the user bounded context.
/// </summary>
public sealed class UserSubscription : AggregateRoot<UserSubscriptionId>
{
    #region Constructor

    /// <summary>
    /// Initializes a new instance of the <see cref="UserSubscription"/> class.
    /// </summary>
    private UserSubscription() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="UserSubscription"/> class.
    /// </summary>
    /// <param name="userId">The user ID associated with this permission.</param>
    /// <param name="expirationDate">The expiration date of the user subscription.</param>
    private UserSubscription(UserId userId, DateTime expirationDate)
        : this()
    {
        Id = UserSubscriptionId.Create(Ensure.NotNull(userId, nameof(userId)));
        ExpirationDate = expirationDate;
        _userSubscriptionStatusId = UserSubscriptionStatus.Active;
    }

    #endregion

    #region Properties

    /// <summary>
    /// Gets the expiry date of the user subscription.
    /// </summary>
    public DateTime ExpirationDate { get; private set; }

    /// <summary>
    /// Gets the user subscription status.
    /// </summary>
    public UserSubscriptionStatus? UserSubscriptionStatus { get; private set; }

    /// <summary>
    /// The ID of the user subscription status.
    /// </summary>
    private int _userSubscriptionStatusId;

    #endregion

    #region Methods

    #region Factory methods

    /// <summary>
    /// Creates a new instance of the <see cref="UserSubscription"/> class.
    /// </summary>
    /// <param name="subscriptionPaymentId">The subscription payment identifier.</param>
    /// <param name="userId">The user ID associated with this permission.</param>
    /// <param name="expirationDate">The expiration date of the user subscription.</param>
    /// <param name="subscriptionCode">The code associated with the subscription.</param>
    /// <returns>The new instance of the <see cref="UserSubscription"/> class.</returns>
    public static UserSubscription Create(
        Guid subscriptionPaymentId,
        UserId userId,
        DateTime expirationDate,
        string subscriptionCode
    )
    {
        Ensure.NotNull(userId, nameof(userId));
        Ensure.NotEmpty(subscriptionPaymentId, nameof(subscriptionPaymentId));
        Ensure.NotEmpty(subscriptionCode, nameof(subscriptionCode));
        Ensure.NotEmpty(expirationDate, nameof(expirationDate));
        CheckRule(new ExpirationDateMustBeGreaterThanTodayRule(expirationDate));

        var userSubscription = new UserSubscription(userId, expirationDate);
        userSubscription.AddDomainEvent(
            new UserSubscriptionExpirationDateChangedDomainEvent(
                SubscriptionPaymentId: subscriptionPaymentId,
                UserId: userSubscription.Id.Value,
                ExpirationDate: expirationDate,
                subscriptionCode,
                IsFirstTime: true
            )
        );

        return userSubscription;
    }

    #endregion

    /// <summary>
    /// Changes the expiration date of the user subscription.
    /// </summary>
    /// <param name="subscriptionPaymentId">The subscription payment identifier.</param>
    /// <param name="expirationDate">The new expiration date of the user subscription.</param>
    /// <param name="subscriptionCode">The code associated with the subscription.</param>
    public void ChangeExpirationDate(Guid subscriptionPaymentId, DateTime expirationDate, string subscriptionCode)
    {
        Ensure.NotEmpty(subscriptionPaymentId, nameof(subscriptionPaymentId));
        Ensure.NotEmpty(subscriptionCode, nameof(subscriptionCode));
        Ensure.NotEmpty(expirationDate, nameof(expirationDate));
        CheckRule(new ExpirationDateMustBeGreaterThanTodayRule(expirationDate));

        ExpirationDate = expirationDate;
        Activate();

        AddDomainEvent(
            new UserSubscriptionExpirationDateChangedDomainEvent(
                SubscriptionPaymentId: subscriptionPaymentId,
                UserId: Id.Value,
                ExpirationDate: ExpirationDate,
                subscriptionCode,
                IsFirstTime: false
            )
        );
    }

    /// <summary>
    /// Expires the user subscription.
    /// </summary>
    public void Expire()
    {
        if (_userSubscriptionStatusId != UserSubscriptionStatus.Active)
        {
            return;
        }

        _userSubscriptionStatusId = UserSubscriptionStatus.Expired;
        AddDomainEvent(new UserSubscriptionExpiredDomainEvent(Id.Value));
    }

    /// <summary>
    /// Gets the user subscription status ID.
    /// </summary>
    /// <returns>The user subscription status ID.</returns>
    public int GetUserSubscriptionStatusId()
    {
        return _userSubscriptionStatusId;
    }

    /// <summary>
    /// Checks if the user subscription is active.
    /// </summary>
    /// <returns>The result of the check.</returns>
    public bool IsMembershipActive()
    {
        return _userSubscriptionStatusId == UserSubscriptionStatus.Active;
    }

    /// <summary>
    /// Activates the user subscription.
    /// </summary>
    private void Activate()
    {
        if (ExpirationDate > SystemClock.Now && _userSubscriptionStatusId == UserSubscriptionStatus.Expired)
        {
            _userSubscriptionStatusId = UserSubscriptionStatus.Active;
        }
    }

    #endregion
}
