using BuildingBlocks.DDD.SeedWork.Primitives;

namespace Account.Domain.Aggregates.PermissionAggregate.ValueObjects;

/// <summary>
/// Represents a permission identifier.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="PermissionId"/> class.
/// </remarks>
/// <param name="Value">The value of the permission identifier.</param>
public sealed record PermissionId(Guid Value) : TypedIdValueBase(Value)
{
    /// <summary>
    /// Initializes a new instance of the <see cref="PermissionId"/> class.
    /// </summary>
    /// <param name="value">The value.</param>
    /// <returns>The <see cref="PermissionId"/> permission identifier.</returns>
    public static PermissionId Create(Guid value) => new(value);

    /// <summary>
    /// Implicitly converts the permission identifier to a guid.
    /// </summary>
    /// <param name="permissionId">The permission identifier.</param>
    public static implicit operator Guid(PermissionId permissionId) => permissionId.Value;

    /// <summary>
    /// Implicitly converts the guid to a permission identifier.
    /// </summary>
    /// <param name="permissionId">The permission identifier.</param>
    public static implicit operator PermissionId(Guid permissionId) => Create(permissionId);
}
