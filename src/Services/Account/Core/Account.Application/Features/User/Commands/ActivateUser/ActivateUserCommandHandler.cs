using Account.Domain.Aggregates.UserAggregate.Data;
using Account.Domain.Aggregates.UserAggregate.Specifications;
using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.Core.Resources;
using BuildingBlocks.CQRS.Commands;

namespace Account.Application.Features.User.Commands.ActivateUser;

/// <summary>
/// Handles the processing of the <see cref="ActivateUserCommand"/> command.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="ActivateUserCommandHandler"/> class.
/// </remarks>
/// <param name="userRepository">The repository for accessing user data.</param>
internal sealed class ActivateUserCommandHandler(IUserRepository userRepository)
    : ICommandHandler<ActivateUserCommand, bool>
{
    /// <inheritdoc />
    public async Task<Result<bool>> Handle(ActivateUserCommand request, CancellationToken cancellationToken)
    {
        var spec = new UserByActivationCodeSpec(request.ActivationCode, isReadOnly: false);
        var user = await userRepository.FirstOrDefaultAsync(spec, cancellationToken);
        if (user is null)
        {
            return SharedErrorMessages.NotFoundErrorMessage(SharedResource.User);
        }

        user.Activate();

        userRepository.Update(user);
        return await userRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
    }
}
