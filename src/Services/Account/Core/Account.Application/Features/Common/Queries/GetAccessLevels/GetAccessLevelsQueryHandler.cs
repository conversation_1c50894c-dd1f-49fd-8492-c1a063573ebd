using BuildingBlocks.Core.Common.Maybe;
using BuildingBlocks.CQRS.Queries;
using MainArchitecture.Application.Models.Common;

namespace Account.Application.Features.Common.Queries.GetAccessLevels;

/// <summary>
/// Handles the processing of the <see cref="GetAccessLevelsQuery"/> query.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="GetAccessLevelsQueryHandler"/> class.
/// </remarks>
/// <param name="databaseQuery">The database query for retrieving access levels.</param>
internal sealed class GetAccessLevelsQueryHandler(IGetAccessLevelsDatabaseQuery databaseQuery)
    : IQueryHandler<GetAccessLevelsQuery, IReadOnlyCollection<NumericOptionVm>>
{
    /// <inheritdoc />
    public Task<Maybe<IReadOnlyCollection<NumericOptionVm>>> Handle(
        GetAccessLevelsQuery request,
        CancellationToken cancellationToken
    )
    {
        return databaseQuery.Handle(cancellationToken);
    }
}
