using System.ComponentModel.DataAnnotations;
using Account.Domain.Resources;
using BuildingBlocks.Core.Resources;

namespace Account.Application.Features.Role.Queries.GetRoleFilteredGroupingPermissions;

/// <summary>
/// Represents a class responsible for get role filtered grouping permissions query request.
/// </summary>
public sealed record GetRoleFilteredGroupingPermissionsQueryRequest
{
    /// <summary>
    /// Initializes a new instance of the <see cref="GetRoleFilteredGroupingPermissionsQueryRequest"/> class.
    /// </summary>
    public GetRoleFilteredGroupingPermissionsQueryRequest() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="GetRoleFilteredGroupingPermissionsQueryRequest"/> class with specified values.
    /// </summary>
    /// <param name="roleId">The role identifier.</param>
    public GetRoleFilteredGroupingPermissionsQueryRequest(Guid roleId)
    {
        RoleId = roleId;
    }

    /// <summary>
    /// Gets the role identifier.
    /// </summary>
    [Display(Name = nameof(DomainResource.Role), ResourceType = typeof(DomainResource))]
    [Required(
        ErrorMessageResourceName = nameof(SharedResource.Required_Error_Message),
        ErrorMessageResourceType = typeof(SharedResource)
    )]
    public Guid RoleId { get; init; }
}
