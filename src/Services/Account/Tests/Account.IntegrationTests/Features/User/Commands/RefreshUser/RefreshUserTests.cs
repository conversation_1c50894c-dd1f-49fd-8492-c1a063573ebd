using Account.API;
using Account.Application.Features.User.Commands.RefreshUser;
using Account.Domain.Resources;
using Account.IntegrationTests.Abstractions;
using Account.Persistence.Persistence;
using Account.TestShared.Fakes.User.Requests;
using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Validation.Common;
using FluentAssertions;
using Xunit.Abstractions;

namespace Account.IntegrationTests.Features.User.Commands.RefreshUser;

/// <summary>
/// Represents the tests for the <see cref="RefreshUserCommand"/> command.
/// </summary>
public class RefreshUserTests : AccountIntegrationTestBase
{
    #region Constructor

    private readonly RefreshUserCommand _command;

    /// <summary>
    /// Initializes a new instance of the <see cref="RefreshUserTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public RefreshUserTests(
        SharedFixtureWithEfCore<Program, AccountContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        _command = new FakeRefreshUserCommand().Generate();
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenAllConditionsAreMet_ShouldRefreshUser()
    {
        // Arrange
        var tokenResult = TokenServiceWireMock.SetupRefreshToken();
        var identityResponse = tokenResult.Value!;

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertSuccessResult();
        response.Value.Should().NotBeNull();
        response.Value!.AccessToken.Should().Be(identityResponse.AccessToken);
        response.Value.RefreshToken.Should().Be(_command.RefreshToken);
        response.Value.ExpiresIn.Should().Be(identityResponse.ExpiresIn);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenIdentityServiceReturnsError_ShouldReturnError()
    {
        // Arrange
        var error = SharedErrorMessages.AuthenticationError(DomainResource.Generate_Token_Error_Message);
        var tokenResult = TokenServiceWireMock.SetupRefreshToken(error);
        var identityResponse = tokenResult.Errors!.First();

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertProblemResult();
        response.Errors.Should().Contain(e => e == identityResponse);
        response.Value.Should().BeNull();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public Task Send_WhenInputIsInvalid_ShouldReturnValidationException()
    {
        // Arrange
        var command = new FakeInvalidRefreshUserCommand().Generate();

        // Act & Assert
        return FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<ValidationException>();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenAlreadyCalledWithSameRequestId_ShouldBeIdempotent()
    {
        // Arrange
        TokenServiceWireMock.SetupRefreshToken();
        await SharedFixture.SendAsync(_command, CancellationToken);

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertIdempotentResult();
    }
}
