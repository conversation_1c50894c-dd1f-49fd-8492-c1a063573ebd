using Account.API;
using Account.Application.Features.Permission.Commands.UpdatePermissionCompanyDataReplication;
using Account.IntegrationTests.Abstractions;
using Account.Persistence.Persistence;
using Account.TestShared.Fakes.Permission.Requests;
using Account.TestShared.Helpers;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Validation.Common;
using FluentAssertions;
using MainArchitecture.Application.Models.Organization;
using Xunit.Abstractions;

namespace Account.IntegrationTests.Features.Permission.Commands.UpdatePermissionCompanyDataReplication;

/// <summary>
/// Represents the tests for the <see cref="UpdatePermissionCompanyDataReplicationCommand"/> command.
/// </summary>
public class UpdatePermissionCompanyDataReplicationTests : AccountIntegrationTestBase
{
    #region Constructor

    private readonly UpdatePermissionCompanyDataReplicationCommand _command;

    /// <summary>
    /// Initializes a new instance of the <see cref="UpdatePermissionCompanyDataReplicationTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public UpdatePermissionCompanyDataReplicationTests(
        SharedFixtureWithEfCore<Program, AccountContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        SharedFixture.SetUserAndOrganization();
        _command = new FakeUpdatePermissionCompanyDataReplicationCommand().Generate();
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenAllConditionsAreMet_ShouldUpdateCompanyDataReplication()
    {
        // Arrange
        await PermissionHelpers.InsertPermissionAsync(
            SharedFixture,
            e2e: false,
            user: SharedFixture.Faker.Random.Guid(),
            companyBranch: new CompanyBranchData(
                CompanyId: _command.CompanyId,
                BranchId: SharedFixture.Faker.Random.Guid()
            ),
            cancellationToken: CancellationToken
        );

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertSuccessResult();
        response.Value.Should().BeTrue();

        var updatedPermission = await PermissionHelpers.GetPermissionsByCompanyIdAsync(
            sharedFixture: SharedFixture,
            companyId: _command.CompanyId,
            cancellationToken: CancellationToken
        );
        updatedPermission[0].Should().NotBeNull();
        updatedPermission[0].CompanyName.Should().Be(_command.CompanyName);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenCompanyNotFound_ShouldReturnFalse()
    {
        // Arrange

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertSuccessResult();
        response.Value.Should().BeFalse();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public Task Send_WhenCompanyIsNotValid_ShouldThrowValidationException()
    {
        // Arrange
        var command = new FakeInvalidUpdatePermissionCompanyDataReplicationCommand().Generate();

        // Act & Assert
        return FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<ValidationException>();
    }
}
