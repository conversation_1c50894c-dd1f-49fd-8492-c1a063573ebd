using Account.Application.Features.Role.Commands.AddPermissionsToRole;
using Account.Domain.Aggregates.RoleAggregate.Data;
using Account.Domain.Aggregates.RoleAggregate.Specifications;
using Account.Domain.Resources;
using Account.TestShared.Fakes.Role.Entities;
using Account.TestShared.Fakes.Role.Requests;
using Account.UnitTests.Abstractions;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using NSubstitute;
using RoleDomain = Account.Domain.Aggregates.RoleAggregate.Entities.Role;

namespace Account.UnitTests.Role.Features.Commands.AddPermissionsToRole;

/// <summary>
/// Represents the unit tests for the <see cref="AddPermissionsToRoleCommandHandler"/> class.
/// </summary>
public class AddPermissionsToRoleCommandHandlerTests : AccountUnitTestBase
{
    #region Constructor

    private readonly IRoleRepository _roleRepository;
    private readonly AddPermissionsToRoleCommandHandler _handler;

    /// <summary>
    /// Initializes a new instance of the <see cref="AddPermissionsToRoleCommandHandlerTests"/> class.
    /// </summary>
    /// <param name="fixture">The test fixture.</param>
    public AddPermissionsToRoleCommandHandlerTests(AccountUnitTestFixture fixture)
        : base(fixture)
    {
        Fixture.SetupUserAccessor(userId: Faker.Random.Guid());
        _roleRepository = Substitute.For<IRoleRepository>();
        _handler = new AddPermissionsToRoleCommandHandler(_roleRepository, Fixture.UserAccessor);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenValidCommand_ShouldAddPermissionsToRole()
    {
        // Arrange
        var command = new FakeAddPermissionsToRoleCommand().Generate();
        var role = new FakeRole().Generate();

        _roleRepository
            .FirstOrDefaultAsync(Arg.Any<RoleByIdAndOwnerSpec>(), Arg.Any<CancellationToken>())
            .Returns(role);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.AssertSuccessResult();
        await AssertRepositoryUpdateAsync(_roleRepository, role);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenRoleNotFound_ShouldReturnError()
    {
        // Arrange
        var command = new FakeAddPermissionsToRoleCommand().Generate();

        _roleRepository
            .FirstOrDefaultAsync(Arg.Any<RoleByIdAndOwnerSpec>(), Arg.Any<CancellationToken>())
            .Returns((RoleDomain?)null);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.AssertNotFoundError(DomainResource.Role);
        await AssertRepositoryUpdateCancelledAsync(_roleRepository);
    }
}
