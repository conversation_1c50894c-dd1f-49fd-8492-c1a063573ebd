using Account.Application.Features.Role.Queries.GetRoleFilteredGroupingPermissions;
using Account.TestShared.Fakes.Role.Requests;
using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using FluentValidation.TestHelper;
using Microsoft.Extensions.Logging.Abstractions;

namespace Account.UnitTests.Role.Features.Queries.GetRoleFilteredGroupingPermissions;

/// <summary>
/// Represents the unit tests for the <see cref="GetRoleFilteredGroupingPermissionsQueryRequestValidator"/> class.
/// </summary>
public class GetRoleFilteredGroupingPermissionsQueryRequestValidatorTests
{
    #region Constructor

    private readonly GetRoleFilteredGroupingPermissionsQueryRequestValidator _validator;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetRoleFilteredGroupingPermissionsQueryRequestValidatorTests"/> class.
    /// </summary>
    public GetRoleFilteredGroupingPermissionsQueryRequestValidatorTests()
    {
        _validator = new GetRoleFilteredGroupingPermissionsQueryRequestValidator(
            NullLogger<GetRoleFilteredGroupingPermissionsQueryRequestValidator>.Instance
        );
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenInputIsValid_ShouldReturnTrue()
    {
        // Arrange
        var query = new FakeGetRoleFilteredGroupingPermissionsQueryRequest().Generate();

        // Act
        var result = _validator.TestValidate(query);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenInputIsInvalid_ShouldReturnFalse()
    {
        // Arrange
        var query = new FakeInvalidGetRoleFilteredGroupingPermissionsQueryRequest().Generate();

        // Act
        var result = _validator.TestValidate(query);

        // Assert
        result.IsValid.Should().BeFalse();
        result.ShouldHaveValidationErrorFor(x => x.RoleId);
    }
}
