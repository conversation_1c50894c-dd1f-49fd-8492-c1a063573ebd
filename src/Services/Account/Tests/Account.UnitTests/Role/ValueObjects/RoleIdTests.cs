using Account.Domain.Aggregates.RoleAggregate.ValueObjects;
using BuildingBlocks.Core.Exception.Types;
using BuildingBlocks.Core.IdGenerator;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Tests.UnitTests.Abstractions;
using FluentAssertions;

namespace Account.UnitTests.Role.ValueObjects;

/// <summary>
/// Represents the base test for the role ID.
/// </summary>
public abstract class RoleIdBaseTest : BaseDomainUnitTest
{
    #region Fields

    /// <summary>
    /// The role ID.
    /// </summary>
    protected readonly Guid FakeGuid = Faker.Random.Guid();

    #endregion
}

/// <summary>
/// Tests for <see cref="RoleId"/> class.
/// </summary>
public class RoleIdTests : RoleIdBaseTest
{
    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Create_WhenValidGuidProvided_ShouldReturnNewRoleId()
    {
        // Arrange

        // Act
        var roleId = RoleId.Create(FakeGuid);

        // Assert
        roleId.Should().NotBeNull();
        roleId.Value.Should().Be(FakeGuid);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Create_WhenEmptyGuidProvided_ShouldThrowCustomAppException()
    {
        // Arrange
        var guid = IdGenerator.EmptyId;

        // Act & Assert
        FluentActions.Invoking(() => RoleId.Create(guid)).Should().Throw<CustomAppException>();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Implicit_WhenConversionToGuid_ShouldReturnCorrectGuid()
    {
        // Arrange
        var roleId = RoleId.Create(FakeGuid);

        // Act
        Guid result = roleId;

        // Assert
        result.Should().Be(FakeGuid);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Implicit_WhenConversionFromGuid_ShouldReturnCorrectRoleId()
    {
        // Arrange

        // Act
        RoleId roleId = FakeGuid;

        // Assert
        roleId.Should().NotBeNull();
        roleId.Value.Should().Be(FakeGuid);
    }
}
