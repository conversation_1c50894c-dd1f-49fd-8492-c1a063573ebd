using Account.Domain.Aggregates.PermissionAggregate.ValueObjects;
using BuildingBlocks.Core.Exception.Types;
using BuildingBlocks.Core.IdGenerator;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Tests.UnitTests.Abstractions;
using FluentAssertions;

namespace Account.UnitTests.Permission.ValueObjects;

/// <summary>
/// Represents the base test for the permission ID.
/// </summary>
public abstract class PermissionIdBaseTest : BaseDomainUnitTest
{
    #region Fields

    /// <summary>
    /// The permission ID.
    /// </summary>
    protected readonly Guid FakeGuid = Faker.Random.Guid();

    #endregion
}

/// <summary>
/// Tests for <see cref="PermissionId"/> class.
/// </summary>
public class PermissionIdTests : PermissionIdBaseTest
{
    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Create_WhenValidGuidProvided_ShouldReturnNewPermissionId()
    {
        // Arrange

        // Act
        var permissionId = PermissionId.Create(FakeGuid);

        // Assert
        permissionId.Should().NotBeNull();
        permissionId.Value.Should().Be(FakeGuid);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Create_WhenEmptyGuidProvided_ShouldThrowCustomAppException()
    {
        // Arrange
        var guid = IdGenerator.EmptyId;

        // Act & Assert
        FluentActions.Invoking(() => PermissionId.Create(guid)).Should().Throw<CustomAppException>();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Implicit_WhenConversionToGuid_ShouldReturnCorrectGuid()
    {
        // Arrange
        var permissionId = PermissionId.Create(FakeGuid);

        // Act
        Guid result = permissionId;

        // Assert
        result.Should().Be(FakeGuid);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Implicit_WhenConversionFromGuid_ShouldReturnCorrectPermissionId()
    {
        // Arrange

        // Act
        PermissionId permissionId = FakeGuid;

        // Assert
        permissionId.Should().NotBeNull();
        permissionId.Value.Should().Be(FakeGuid);
    }
}
