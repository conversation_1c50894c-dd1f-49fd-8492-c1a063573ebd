using Account.Application.Features.Permission.Commands.AddPermissionsToUser;
using Account.Domain.Aggregates.PermissionAggregate.Data;
using Account.Domain.Aggregates.PermissionAggregate.Services;
using Account.Domain.SharedKernel.ValueObjects;
using Account.TestShared.Fakes.Permission.Entities;
using Account.TestShared.Fakes.Permission.Requests;
using Account.UnitTests.Abstractions;
using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.Core.Resources;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds.Organization;
using NSubstitute;
using PermissionDomain = Account.Domain.Aggregates.PermissionAggregate.Entities.Permission;

namespace Account.UnitTests.Permission.Features.Commands.AddPermissionsToUser;

/// <summary>
/// Represents the unit tests for the <see cref="AddPermissionsToUserCommandHandler"/> class.
/// </summary>
public class AddPermissionsToUserCommandHandlerTests : AccountUnitTestBase
{
    #region Constructor

    private readonly IPermissionRepository _permissionRepository;
    private readonly IPermissionService _permissionService;
    private readonly AddPermissionsToUserCommandHandler _handler;

    /// <summary>
    /// Initializes a new instance of the <see cref="AddPermissionsToUserCommandHandlerTests"/> class.
    /// </summary>
    /// <param name="fixture">The test fixture.</param>
    public AddPermissionsToUserCommandHandlerTests(AccountUnitTestFixture fixture)
        : base(fixture)
    {
        Fixture.SetupUserAccessor(userId: Faker.Random.Guid());
        _permissionRepository = Substitute.For<IPermissionRepository>();
        _permissionService = Substitute.For<IPermissionService>();
        _handler = new AddPermissionsToUserCommandHandler(
            _permissionRepository,
            _permissionService,
            Fixture.UserAccessor
        );
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenValidCommand_ShouldAddPermissions()
    {
        // Arrange
        var command = new FakeAddPermissionsToUserCommand().Generate();
        var permissions = new FakePermission().Generate(2);

        _permissionService
            .DetermineNewPermissionsAsync(
                userId: Arg.Any<UserId>(),
                ownerId: Arg.Any<UserId>(),
                companyId: Arg.Any<CompanyId>(),
                companyName: Arg.Any<string>(),
                branches: Arg.Any<IReadOnlyCollection<(BranchId BranchId, string BranchName)>>(),
                userPermissionSystemAccesses: Arg.Any<IReadOnlyCollection<SystemAccess>>(),
                cancellationToken: Arg.Any<CancellationToken>()
            )
            .Returns(Task.FromResult(Result.Success<IReadOnlyCollection<PermissionDomain>>(permissions)));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.AssertSuccessResult();
        await AssertRepositoryCreateRangeAsync(_permissionRepository, permissions);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenUserNotFound_ShouldReturnError()
    {
        // Arrange
        var command = new FakeAddPermissionsToUserCommand().Generate();

        _permissionService
            .DetermineNewPermissionsAsync(
                userId: Arg.Any<UserId>(),
                ownerId: Arg.Any<UserId>(),
                companyId: Arg.Any<CompanyId>(),
                companyName: Arg.Any<string>(),
                branches: Arg.Any<IReadOnlyCollection<(BranchId BranchId, string BranchName)>>(),
                userPermissionSystemAccesses: Arg.Any<IReadOnlyCollection<SystemAccess>>(),
                cancellationToken: Arg.Any<CancellationToken>()
            )
            .Returns(
                Task.FromResult(
                    Result.Error<IReadOnlyCollection<PermissionDomain>>(
                        SharedErrorMessages.NotFoundErrorMessage(SharedResource.User)
                    )
                )
            );

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.AssertNotFoundError(SharedResource.User);
        await AssertRepositoryCreateCancelledAsync(_permissionRepository);
    }
}
