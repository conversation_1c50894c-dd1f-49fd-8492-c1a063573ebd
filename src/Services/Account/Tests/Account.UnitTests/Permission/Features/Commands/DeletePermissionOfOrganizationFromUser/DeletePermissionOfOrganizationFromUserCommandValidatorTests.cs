using Account.Application.Features.Permission.Commands.DeletePermissionOfOrganizationFromUser;
using Account.TestShared.Fakes.Permission.Requests;
using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using FluentValidation.TestHelper;
using Microsoft.Extensions.Logging.Abstractions;

namespace Account.UnitTests.Permission.Features.Commands.DeletePermissionOfOrganizationFromUser;

/// <summary>
/// Represents the unit tests for the <see cref="DeletePermissionOfOrganizationFromUserCommandValidator"/> class.
/// </summary>
public class DeletePermissionOfOrganizationFromUserCommandValidatorTests
{
    #region Constructor

    private readonly DeletePermissionOfOrganizationFromUserCommandValidator _validator;

    /// <summary>
    /// Initializes a new instance of the <see cref="DeletePermissionOfOrganizationFromUserCommandValidatorTests"/> class.
    /// </summary>
    public DeletePermissionOfOrganizationFromUserCommandValidatorTests()
    {
        _validator = new DeletePermissionOfOrganizationFromUserCommandValidator(
            NullLogger<DeletePermissionOfOrganizationFromUserCommandValidator>.Instance
        );
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenValidInput_ShouldNotHaveValidationError()
    {
        // Arrange
        var command = new FakeDeletePermissionOfOrganizationFromUserCommand().Generate();

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenInvalidInput_ShouldHaveValidationError()
    {
        // Arrange
        var command = new FakeInvalidDeletePermissionOfOrganizationFromUserCommand().Generate();

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.ShouldHaveValidationErrorFor(x => x.UserId);
        result.ShouldHaveValidationErrorFor(x => x.OwnerId);
        result.ShouldHaveValidationErrorFor(x => x.Organization);
    }
}
