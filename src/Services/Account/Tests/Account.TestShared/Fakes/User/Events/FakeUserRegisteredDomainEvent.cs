using Account.Domain.Aggregates.UserAggregate.Events;
using Bogus;
using MainArchitecture.TestShared.Fakes.Common.ValueObjects;

namespace Account.TestShared.Fakes.User.Events;

/// <summary>
/// Represents a fake user registered domain event.
/// </summary>
public sealed class FakeUserRegisteredDomainEvent : Faker<UserRegisteredDomainEvent>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeUserRegisteredDomainEvent"/> class.
    /// </summary>
    public FakeUserRegisteredDomainEvent()
    {
        CustomInstantiator(f =>
        {
            var email = new FakeEmail().Generate();

            return new UserRegisteredDomainEvent(f.Person.FullName, email, f.Random.Guid());
        });
    }
}
