using Account.Application.Features.User.Commands.UpdateUser;
using AutoBogus;
using BuildingBlocks.Core.Constants;
using MainArchitecture.TestShared.Fakes.Common.Models;

namespace Account.TestShared.Fakes.User.Requests;

/// <summary>
/// Represents a fake update user command.
/// </summary>
public sealed class FakeUpdateUserCommand : AutoFaker<UpdateUserCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeUpdateUserCommand"/> class.
    /// </summary>
    public FakeUpdateUserCommand()
    {
        RuleFor(x => x.FirstName, f => f.Person.FirstName);
        RuleFor(x => x.LastName, f => f.Person.LastName);
        RuleFor(
            x => x.NationalCode,
            f =>
                f.Random.String2(
                    GlobalDomainConstValues.ContactNationalCodeMinLength,
                    GlobalDomainConstValues.ContactNationalCodeMaxLength
                )
        );
        RuleFor(x => x.Mobile, _ => new FakeMobileDto().Generate());
        RuleFor(x => x.Address, _ => new FakeAddressData().Generate());
    }
}

/// <summary>
/// Represents a fake invalid update user command.
/// </summary>
public sealed class FakeInvalidUpdateUserCommand : AutoFaker<UpdateUserCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidUpdateUserCommand"/> class.
    /// </summary>
    public FakeInvalidUpdateUserCommand()
    {
        RuleFor(x => x.FirstName, _ => string.Empty);
        RuleFor(x => x.LastName, _ => string.Empty);
    }
}
