using Account.Persistence.Persistence;
using BuildingBlocks.Core.Types.Extensions;
using BuildingBlocks.Persistence.Contracts;
using BuildingBlocks.Persistence.EntityConfiguration.Entities;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AdministratorDomain = Account.Domain.Aggregates.AdministratorAggregate.Entities.Administrator;
using UserDomain = Account.Domain.Aggregates.UserAggregate.Entities.User;

namespace Account.Persistence.Domain.Administrator.Configurations.Entities;

/// <summary>
/// Represents the entity configuration for the <see cref="AdministratorDomain"/> aggregate.
/// </summary>
internal sealed class AdministratorConfiguration
    : EntityConfigConfigurationContracts<AdministratorDomain>,
        IEntityTypeConfiguration<AdministratorDomain>
{
    /// <inheritdoc />
    public void Configure(EntityTypeBuilder<AdministratorDomain> builder) =>
        builder.Tap(ConfigDataStructure).Tap(ConfigRelationships);

    #region Overrides

    /// <inheritdoc />
    public override void ConfigDataStructure(EntityTypeBuilder<AdministratorDomain> builder)
    {
        builder.ConfigureAggregateRoot<AdministratorDomain, UserId>(
            tableName: nameof(AccountContext.Administrators),
            schemaName: SchemaNames.User
        );
    }

    /// <inheritdoc />
    public override void ConfigRelationships(EntityTypeBuilder<AdministratorDomain> builder)
    {
        builder.HasOne<UserDomain>().WithOne().HasForeignKey<AdministratorDomain>(a => a.Id).IsRequired();
    }

    #endregion
}
