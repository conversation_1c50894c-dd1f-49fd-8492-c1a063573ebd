using Account.Domain.Aggregates.PermissionAggregate.Data;
using Account.Domain.Aggregates.PermissionAggregate.ValueObjects;
using Account.Persistence.Persistence;
using BuildingBlocks.Persistence.Repositories;
using BuildingBlocks.Persistence.Specification;
using PermissionDomain = Account.Domain.Aggregates.PermissionAggregate.Entities.Permission;

namespace Account.Persistence.Domain.Permission.Data;

/// <summary>
/// Represents a repository implementation for the <see cref="Permission"/> aggregate.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="PermissionRepository"/> class.
/// </remarks>
/// <param name="dbContext">The AccountContext instance for database access.</param>
internal sealed class PermissionRepository(AccountContext dbContext)
    : Repository<PermissionDomain, PermissionId>(dbContext, SpecificationBaseEvaluator.Instance),
        IPermissionRepository;
