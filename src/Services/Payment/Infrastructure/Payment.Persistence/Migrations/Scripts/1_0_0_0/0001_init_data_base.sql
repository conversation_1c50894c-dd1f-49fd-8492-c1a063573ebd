IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
GO

BEGIN TRANSACTION;
GO

IF SCHEMA_ID(N'payment') IS NULL EXEC(N'CREATE SCHEMA [payment];');
GO

CREATE TABLE [__DomainEventOutboxMessages] (
    [Id] uniqueidentifier NOT NULL,
    [Name] nvarchar(256) NOT NULL,
    [Content] nvarchar(2048) NOT NULL,
    [CreateDate] datetime2 NOT NULL,
    [ProcessedDate] datetime2 NULL,
    [Error] nvarchar(2048) NULL,
    CONSTRAINT [PK___DomainEventOutboxMessages] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [__MTInboxStates] (
    [Id] bigint NOT NULL IDENTITY,
    [MessageId] uniqueidentifier NOT NULL,
    [ConsumerId] uniqueidentifier NOT NULL,
    [LockId] uniqueidentifier NOT NULL,
    [RowVersion] rowversion NULL,
    [Received] datetime2 NOT NULL,
    [ReceiveCount] int NOT NULL,
    [ExpirationTime] datetime2 NULL,
    [Consumed] datetime2 NULL,
    [Delivered] datetime2 NULL,
    [LastSequenceNumber] bigint NULL,
    CONSTRAINT [PK___MTInboxStates] PRIMARY KEY ([Id]),
    CONSTRAINT [AK___MTInboxStates_MessageId_ConsumerId] UNIQUE ([MessageId], [ConsumerId])
);
GO

CREATE TABLE [__MTOutboxMessages] (
    [SequenceNumber] bigint NOT NULL IDENTITY,
    [EnqueueTime] datetime2 NULL,
    [SentTime] datetime2 NOT NULL,
    [Headers] nvarchar(max) NULL,
    [Properties] nvarchar(max) NULL,
    [InboxMessageId] uniqueidentifier NULL,
    [InboxConsumerId] uniqueidentifier NULL,
    [OutboxId] uniqueidentifier NULL,
    [MessageId] uniqueidentifier NOT NULL,
    [ContentType] nvarchar(256) NOT NULL,
    [MessageType] nvarchar(max) NOT NULL,
    [Body] nvarchar(max) NOT NULL,
    [ConversationId] uniqueidentifier NULL,
    [CorrelationId] uniqueidentifier NULL,
    [InitiatorId] uniqueidentifier NULL,
    [RequestId] uniqueidentifier NULL,
    [SourceAddress] nvarchar(256) NULL,
    [DestinationAddress] nvarchar(256) NULL,
    [ResponseAddress] nvarchar(256) NULL,
    [FaultAddress] nvarchar(256) NULL,
    [ExpirationTime] datetime2 NULL,
    CONSTRAINT [PK___MTOutboxMessages] PRIMARY KEY ([SequenceNumber])
);
GO

CREATE TABLE [__MTOutboxStates] (
    [OutboxId] uniqueidentifier NOT NULL,
    [LockId] uniqueidentifier NOT NULL,
    [RowVersion] rowversion NULL,
    [Created] datetime2 NOT NULL,
    [Delivered] datetime2 NULL,
    [LastSequenceNumber] bigint NULL,
    CONSTRAINT [PK___MTOutboxStates] PRIMARY KEY ([OutboxId])
);
GO

CREATE TABLE [__ProcessedClientRequests] (
    [Id] uniqueidentifier NOT NULL,
    [Name] nvarchar(256) NOT NULL,
    [CreateDate] datetime2 NOT NULL,
    CONSTRAINT [PK___ProcessedClientRequests] PRIMARY KEY ([Id], [Name])
);
GO

CREATE TABLE [__ProcessedDomainEvents] (
    [Id] uniqueidentifier NOT NULL,
    [Name] nvarchar(256) NOT NULL,
    [CreateDate] datetime2 NOT NULL,
    CONSTRAINT [PK___ProcessedDomainEvents] PRIMARY KEY ([Id], [Name])
);
GO

CREATE TABLE [__StoredEvents] (
    [EventId] uniqueidentifier NOT NULL,
    [EventType] nvarchar(256) NOT NULL,
    [EventTypeAssemblyQualifiedName] nvarchar(1024) NOT NULL,
    [EntityId] uniqueidentifier NOT NULL,
    [EntityType] nvarchar(256) NOT NULL,
    [Data] nvarchar(2048) NOT NULL,
    [CreateDate] datetime2 NOT NULL,
    CONSTRAINT [PK___StoredEvents] PRIMARY KEY ([EventId])
);
GO

CREATE TABLE [payment].[PaymentStatuses] (
    [Id] int NOT NULL,
    [Name] nvarchar(25) NOT NULL,
    CONSTRAINT [PK_PaymentStatuses] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [payment].[PaymentTypes] (
    [Id] int NOT NULL,
    [Name] nvarchar(25) NOT NULL,
    CONSTRAINT [PK_PaymentTypes] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [payment].[Payments] (
    [Id] uniqueidentifier NOT NULL,
    [UserId] uniqueidentifier NOT NULL,
    [EntityId] uniqueidentifier NOT NULL,
    [TransactionId] uniqueidentifier NOT NULL,
    [TransactionToken] nvarchar(50) NULL,
    [PaymentStatusId] int NOT NULL,
    [PaymentTypeId] int NOT NULL,
    [CountryCode] nvarchar(3) NOT NULL,
    [MoneyValue_Currency] nvarchar(3) NOT NULL,
    [MoneyValue_Value] decimal(18,2) NOT NULL,
    [CreateDate] datetime2 NOT NULL,
    [LastModifiedDate] datetime2 NULL,
    [Version] int NOT NULL,
    CONSTRAINT [PK_Payments] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Payments_PaymentStatuses_PaymentStatusId] FOREIGN KEY ([PaymentStatusId]) REFERENCES [payment].[PaymentStatuses] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_Payments_PaymentTypes_PaymentTypeId] FOREIGN KEY ([PaymentTypeId]) REFERENCES [payment].[PaymentTypes] ([Id]) ON DELETE CASCADE
);
GO

CREATE INDEX [IX___MTInboxStates_Delivered] ON [__MTInboxStates] ([Delivered]);
GO

CREATE INDEX [IX___MTOutboxMessages_EnqueueTime] ON [__MTOutboxMessages] ([EnqueueTime]);
GO

CREATE INDEX [IX___MTOutboxMessages_ExpirationTime] ON [__MTOutboxMessages] ([ExpirationTime]);
GO

CREATE UNIQUE INDEX [IX___MTOutboxMessages_InboxMessageId_InboxConsumerId_SequenceNumber] ON [__MTOutboxMessages] ([InboxMessageId], [InboxConsumerId], [SequenceNumber]) WHERE [InboxMessageId] IS NOT NULL AND [InboxConsumerId] IS NOT NULL;
GO

CREATE UNIQUE INDEX [IX___MTOutboxMessages_OutboxId_SequenceNumber] ON [__MTOutboxMessages] ([OutboxId], [SequenceNumber]) WHERE [OutboxId] IS NOT NULL;
GO

CREATE INDEX [IX___MTOutboxStates_Created] ON [__MTOutboxStates] ([Created]);
GO

CREATE INDEX [IX_Payments_PaymentStatusId] ON [payment].[Payments] ([PaymentStatusId]);
GO

CREATE INDEX [IX_Payments_PaymentTypeId] ON [payment].[Payments] ([PaymentTypeId]);
GO

CREATE INDEX [IX_Payments_UserId] ON [payment].[Payments] ([UserId]);
GO

CREATE UNIQUE INDEX [IX_Payments_UserId_EntityId] ON [payment].[Payments] ([UserId], [EntityId]);
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20240715144826_InitDataBase', N'8.0.7');
GO

COMMIT;
GO

