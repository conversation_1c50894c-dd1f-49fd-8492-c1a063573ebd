using MainArchitecture.Domain.SharedKernel.StronglyTypedIds;
using MainArchitecture.Domain.SharedKernel.ValueObjects;

namespace Payment.Domain.Aggregates.PaymentAggregate.Snapshots;

/// <summary>
/// Represents the snapshot of the Payment entity.
/// </summary>
/// <param name="UserId">The user identifier.</param>
/// <param name="EntityId">The entity identifier.</param>
/// <param name="PaymentTypeId">The payment type identifier.</param>
/// <param name="Price">The price.</param>
/// <param name="Email">The user email.</param>
public sealed record PaymentSnapshot(UserId UserId, Guid EntityId, int PaymentTypeId, MoneyValue Price, Email Email);
