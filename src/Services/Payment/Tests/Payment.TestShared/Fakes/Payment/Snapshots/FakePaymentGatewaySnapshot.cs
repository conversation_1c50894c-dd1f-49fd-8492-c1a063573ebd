using Bogus;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds;
using MainArchitecture.TestShared.Fakes.Common.Enumerations;
using Payment.Domain.Aggregates.PaymentAggregate.Enumerations;
using Payment.Domain.Aggregates.PaymentAggregate.Snapshots;

namespace Payment.TestShared.Fakes.Payment.Snapshots;

/// <summary>
/// Represents a fake payment gateway snapshot.
/// </summary>
public sealed class FakePaymentGatewaySnapshot : Faker<PaymentGatewaySnapshot>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakePaymentGatewaySnapshot"/> class.
    /// </summary>
    public FakePaymentGatewaySnapshot()
    {
        CustomInstantiator(f =>
        {
            var paymentTypeId = new FakeEnumeration<PaymentType>().Generate();
            var userId = UserId.Create(f.Random.Guid());
            var entityId = f.Random.Guid();
            var extendingValue = f.Random.Int(1, 100);

            return new PaymentGatewaySnapshot(entityId, userId, paymentTypeId, extendingValue);
        });
    }
}
