using BuildingBlocks.Email.Models;

namespace EmailService.Models.Account;

/// <summary>
/// Represents an email notification sent for user activation (Activation).
/// </summary>
/// <param name="ActivationCode">The activation code associated with the user.</param>
/// <param name="Email">The recipient email address.</param>
/// <param name="Username">The username of the user being activated.</param>
public sealed record ActivationEmail(Guid ActivationCode, string Email, string Username)
    : EmailObject(Email, "User Activation");
