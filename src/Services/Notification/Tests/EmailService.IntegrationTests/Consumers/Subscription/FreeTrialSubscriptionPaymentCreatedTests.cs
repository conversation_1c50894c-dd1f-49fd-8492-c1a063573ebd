using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.XunitCategories;
using EmailService.Consumers.Subscription;
using EmailService.DbContext;
using EmailService.IntegrationTests.Abstractions;
using MainArchitecture.IntegrationEvents.Events.Subscription;
using MainArchitecture.TestShared.Fakes.Subscription.Events;
using Xunit.Abstractions;

namespace EmailService.IntegrationTests.Consumers.Subscription;

/// <summary>
/// Represents the integration test for the <see cref="FreeTrialSubscriptionPaymentCreatedIntegrationEventConsumer"/> class.
/// </summary>
public class FreeTrialSubscriptionPaymentCreatedTests : EmailServiceIntegrationTestBase
{
    #region Constructor

    private readonly FreeTrialSubscriptionPaymentCreatedIntegrationEvent _freeTrialSubscriptionPaymentCreatedIntegrationEvent;

    /// <summary>
    /// Initializes a new instance of the <see cref="FreeTrialSubscriptionPaymentCreatedTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public FreeTrialSubscriptionPaymentCreatedTests(
        SharedFixtureWithEfCore<Program, EmailDbContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        _freeTrialSubscriptionPaymentCreatedIntegrationEvent =
            new FakeFreeTrialSubscriptionPaymentCreatedIntegrationEvent().Generate();
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task IntegrationEvent_WhenPublished_ShouldConsumedByExistingConsumerThroughTheBroker()
    {
        // Arrange

        // Act
        await SharedFixture.PublishIntegrationEventAsync(
            _freeTrialSubscriptionPaymentCreatedIntegrationEvent,
            CancellationToken
        );

        // Assert
        await SharedFixture.WaitForConsuming<FreeTrialSubscriptionPaymentCreatedIntegrationEvent>(CancellationToken);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task IntegrationEvent_WhenPublished_ShouldConsumedByFreeTrialSubscriptionPaymentCreatedIntegrationEventConsumerThroughTheBroker()
    {
        // Arrange

        // Act
        await SharedFixture.PublishIntegrationEventAsync(
            _freeTrialSubscriptionPaymentCreatedIntegrationEvent,
            CancellationToken
        );

        // Assert
        await SharedFixture.WaitForConsuming<
            FreeTrialSubscriptionPaymentCreatedIntegrationEvent,
            FreeTrialSubscriptionPaymentCreatedIntegrationEventConsumer
        >(CancellationToken);
    }
}
