using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.XunitCategories;
using EmailService.Consumers.Account;
using EmailService.DbContext;
using EmailService.IntegrationTests.Abstractions;
using MainArchitecture.IntegrationEvents.Events.Account;
using MainArchitecture.TestShared.Fakes.Account.Events;
using Xunit.Abstractions;

namespace EmailService.IntegrationTests.Consumers.Account;

/// <summary>
/// Represents the integration test for the <see cref="UserMembershipExpiredIntegrationEventConsumer"/> class.
/// </summary>
public class UserMembershipExpiredTests : EmailServiceIntegrationTestBase
{
    #region Constructor

    private readonly UserMembershipExpiredIntegrationEvent _userMembershipExpiredIntegrationEvent;

    /// <summary>
    /// Initializes a new instance of the <see cref="UserMembershipExpiredTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public UserMembershipExpiredTests(
        SharedFixtureWithEfCore<Program, EmailDbContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        _userMembershipExpiredIntegrationEvent = new FakeUserMembershipExpiredIntegrationEvent().Generate();
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task IntegrationEvent_WhenPublished_ShouldConsumedByExistingConsumerThroughTheBroker()
    {
        // Arrange

        // Act
        await SharedFixture.PublishIntegrationEventAsync(_userMembershipExpiredIntegrationEvent, CancellationToken);

        // Assert
        await SharedFixture.WaitForConsuming<UserMembershipExpiredIntegrationEvent>(CancellationToken);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task IntegrationEvent_WhenPublished_ShouldConsumedByUserMembershipExpiredIntegrationEventConsumerThroughTheBroker()
    {
        // Arrange

        // Act
        await SharedFixture.PublishIntegrationEventAsync(_userMembershipExpiredIntegrationEvent, CancellationToken);

        // Assert
        await SharedFixture.WaitForConsuming<
            UserMembershipExpiredIntegrationEvent,
            UserMembershipExpiredIntegrationEventConsumer
        >(CancellationToken);
    }
}
