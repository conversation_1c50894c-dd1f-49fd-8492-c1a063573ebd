using BuildingBlocks.CQRS.Commands;
using MainArchitecture.Application.Models.Common;

namespace Warehouse.Application.Features.SupplierReceipt.Commands.ChangeSupplierReceiptDetailPrice;

/// <summary>
/// This command is handled by <see cref="ChangeSupplierReceiptDetailPriceCommandHandler"/> class.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="ChangeSupplierReceiptDetailPriceCommand"/> class.
/// </remarks>
/// <param name="SupplierReceiptDetailId">The supplier receipt detail identifier.</param>
/// <param name="BuyPrice">The buy price.</param>
/// <param name="SellPrice">The sell price.</param>
/// <param name="ConsumerPrice">The consumer price.</param>
/// <param name="Discount">The discount.</param>
public sealed record ChangeSupplierReceiptDetailPriceCommand(
    Guid SupplierReceiptDetailId,
    MoneyDto? BuyPrice,
    MoneyDto? SellPrice,
    MoneyDto? ConsumerPrice,
    MoneyDto? Discount
) : IdempotentCommand<bool>;
