using BuildingBlocks.CQRS.Commands;

namespace Warehouse.Application.Features.SupplierReturn.Commands.SendSupplierReturn;

/// <summary>
/// Represents a command to send a supplier return. This command is handled by <see cref="SendSupplierReturnCommandHandler"/> class.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="SendSupplierReturnCommand"/> class.
/// </remarks>
/// <param name="SupplierReturnId">The supplier return identifier.</param>
public sealed record SendSupplierReturnCommand(Guid SupplierReturnId) : IdempotentCommand<bool>;
