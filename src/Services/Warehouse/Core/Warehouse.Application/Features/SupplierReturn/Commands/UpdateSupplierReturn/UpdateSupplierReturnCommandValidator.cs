using BuildingBlocks.Core.Resources;
using BuildingBlocks.Core.Types.Extensions;
using BuildingBlocks.Validation.Extensions;
using FluentValidation;
using Microsoft.Extensions.Logging;
using Warehouse.Application.Features.SupplierReturn.Models.Dto;
using Warehouse.Domain.Resources;

namespace Warehouse.Application.Features.SupplierReturn.Commands.UpdateSupplierReturn;

/// <summary>
/// Validator for the <see cref="UpdateSupplierReturnCommand"/> class.
/// </summary>
internal sealed class UpdateSupplierReturnCommandValidator : AbstractValidator<UpdateSupplierReturnCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="UpdateSupplierReturnCommandValidator"/> class.
    /// </summary>
    /// <param name="logger">The logger.</param>
    public UpdateSupplierReturnCommandValidator(ILogger<UpdateSupplierReturnCommandValidator> logger)
    {
        Include(new SupplierReturnDtoValidator());
        RuleFor(x => x.SupplierReturnId)
            .ValidateGuid(SharedResource.Entity_Id.FormatWithStr(DomainResource.Supplier_Return));
        RuleFor(x => x.SupplierId).ValidateGuid(SharedResource.Entity_Id.FormatWithStr(DomainResource.Supplier));

        if (logger.IsEnabled(LogLevel.Trace))
        {
            logger.LogTrace("INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}
