using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.Core.IdGenerator;
using BuildingBlocks.CQRS.Commands;
using MainArchitecture.Application.Contracts.Infrastructure;
using Warehouse.Domain.Aggregates.SupplierReturnAggregate.Data;
using Warehouse.Domain.Aggregates.SupplierReturnAggregate.Specifications;
using Warehouse.Domain.Resources;

namespace Warehouse.Application.Features.SupplierReturn.Commands.CancelSupplierReturn;

/// <summary>
/// Handles the <see cref="CancelSupplierReturnCommand"/> command.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="CancelSupplierReturnCommandHandler"/> class.
/// </remarks>
/// <param name="supplierReturnRepository">The supplier return repository.</param>
/// <param name="organizationAccessor">The organization accessor.</param>
internal sealed class CancelSupplierReturnCommandHandler(
    ISupplierReturnRepository supplierReturnRepository,
    IOrganizationAccessor organizationAccessor
) : ICommandHandler<CancelSupplierReturnCommand, bool>
{
    public async Task<Result<bool>> Handle(CancelSupplierReturnCommand request, CancellationToken cancellationToken)
    {
        var ownerId = organizationAccessor.GetOrganizationOwnership?.OwnerId ?? IdGenerator.EmptyId;
        var spec = new SupplierReturnByIdAndOwnerSpec(request.SupplierReturnId, ownerId, isReadOnly: false);

        var supplierReturn = await supplierReturnRepository.FirstOrDefaultAsync(spec, cancellationToken);
        if (supplierReturn is null)
        {
            return SharedErrorMessages.NotFoundErrorMessage(DomainResource.Supplier_Return);
        }

        var loadedVersion = supplierReturn.Version;
        supplierReturn.Cancel();

        await supplierReturnRepository.ConcurrencySafeUpdate(supplierReturn, loadedVersion, cancellationToken);
        return await supplierReturnRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
    }
}
