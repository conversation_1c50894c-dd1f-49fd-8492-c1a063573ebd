using BuildingBlocks.CQRS.Commands;

namespace Warehouse.Application.Features.SupplierReturn.Commands.DeleteSupplierReturnDetail;

/// <summary>
/// Represents a command to delete a supplier return detail. This command is handled by <see cref="DeleteSupplierReturnDetailCommandHandler"/> class.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="DeleteSupplierReturnDetailCommand"/> class.
/// </remarks>
/// <param name="SupplierReturnDetailId">The supplier return detail identifier.</param>
public sealed record DeleteSupplierReturnDetailCommand(Guid SupplierReturnDetailId) : IdempotentCommand<bool>;
