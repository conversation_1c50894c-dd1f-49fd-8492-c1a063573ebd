using AutoMapper;
using BuildingBlocks.Core.Common;
using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.Core.Resources;
using BuildingBlocks.CQRS.Commands;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds.Organization;
using MainArchitecture.Domain.SharedKernel.ValueObjects;
using Warehouse.Domain.Aggregates.ProductAggregate.Data;
using Warehouse.Domain.Aggregates.ProductAggregate.Specifications;
using Warehouse.Domain.Aggregates.ProductAggregate.ValueObjects;
using ProductDomain = Warehouse.Domain.Aggregates.ProductAggregate.Entities.Product;

namespace Warehouse.Application.Features.Product.Commands.AddProductPriceFromSupplierReceipt;

/// <summary>
/// Handles the processing of the <see cref="AddProductPriceFromSupplierReceiptCommand"/> command.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="AddProductPriceFromSupplierReceiptCommandHandler"/> class.
/// </remarks>
/// <param name="productRepository">The repository for accessing product data.</param>
/// <param name="mapper">The mapper.</param>
internal sealed class AddProductPriceFromSupplierReceiptCommandHandler(
    IProductRepository productRepository,
    IMapper mapper
) : ICommandHandler<AddProductPriceFromSupplierReceiptCommand, bool>
{
    /// <inheritdoc />
    public async Task<Result<bool>> Handle(
        AddProductPriceFromSupplierReceiptCommand request,
        CancellationToken cancellationToken
    )
    {
        // Find the product
        ProductBatchId? productBatchId = null;
        ProductDomain? currentProduct = null;
        if (request.ProductBatch is not null)
        {
            var spec = new ProductWithBatchesByProductBatchInformationSpec(
                request.ProductId,
                request.ProductBatch.BatchNumber,
                request.ProductBatch.LifeCycleDate.StartDate,
                request.ProductBatch.LifeCycleDate.EndDate,
                isReadOnly: false
            );
            var product = await productRepository.FirstOrDefaultAsync(spec, cancellationToken);
            if (product is not null)
            {
                currentProduct = product;
                productBatchId = product.ProductBatches.First().Id;
            }
        }

        if (currentProduct is null)
        {
            productBatchId = null;
            var productSpec = new ProductByIdSpec(request.ProductId, isReadOnly: false);
            currentProduct = await productRepository.FirstOrDefaultAsync(productSpec, cancellationToken);
        }

        if (currentProduct is null)
        {
            return SharedErrorMessages.NotFoundErrorMessage(SharedResource.Product);
        }

        var loadedVersion = currentProduct.Version;

        var startDate = SystemClock.Now;
        var endDate = startDate.AddMonths(6);
        var dateRange = DateRange.Create(startDate, endDate);

        // Add the price to the product
        currentProduct.AddPrice(
            productBatchId: productBatchId,
            customerId: null,
            customerName: null,
            customerGroupId: null,
            customerGroupName: null,
            branches: [BranchId.Create(request.BranchId)],
            dateRange: dateRange,
            buyPrice: mapper.Map<MoneyValue>(request.BuyPrice),
            sellPrice: mapper.Map<MoneyValue>(request.SellPrice),
            consumerPrice: mapper.Map<MoneyValue>(request.ConsumerPrice),
            authorId: request.AuthorId
        );

        // Save changes
        await productRepository.ConcurrencySafeUpdate(currentProduct, loadedVersion, cancellationToken);
        await productRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);

        return true;
    }
}
