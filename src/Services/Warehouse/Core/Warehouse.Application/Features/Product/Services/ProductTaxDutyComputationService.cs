using MainArchitecture.Domain.SharedKernel.ValueObjects;
using Warehouse.Domain.Aggregates.ProductAggregate.Services;

namespace Warehouse.Application.Features.Product.Services;

/// <summary>
/// Service for computing tax and duty values for a product.
/// </summary>
internal sealed class ProductTaxDutyComputationService : IProductTaxDutyComputationService
{
    /// <inheritdoc />
    public TaxDutyValue ComputeTaxDuty(
        TaxDuty taxDuty,
        MoneyValue fee,
        float totalQuantity,
        MoneyValue? discount = null
    )
    {
        var discountValue = discount ?? MoneyValue.Zero(fee.Currency);
        var totalPrice = totalQuantity * fee;
        var discountedFee = totalPrice - discountValue;
        var taxValue = taxDuty.TaxPercentage * discountedFee / 100;
        var dutyValue = taxDuty.DutyPercentage * discountedFee / 100;
        return TaxDutyValue.Create(taxValue, dutyValue);
    }
}
