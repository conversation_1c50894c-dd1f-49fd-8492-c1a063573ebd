using BuildingBlocks.Validation.Extensions;
using FluentValidation;
using MainArchitecture.Application.Models.Common;
using MainArchitecture.Domain.Constants;
using MainArchitecture.Domain.Resources;
using Warehouse.Domain.Aggregates.ProductAggregate.Enumerations;
using Warehouse.Domain.Constants;
using Warehouse.Domain.Resources;

namespace Warehouse.Application.Features.Product.Models.Dto;

/// <summary>
/// Validates the <see cref="IProductBatchDto"/> before it's processed by command validators.
/// </summary>
internal sealed class ProductBatchDtoValidator : AbstractValidator<IProductBatchDto>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="ProductBatchDtoValidator"/> class.
    /// </summary>
    public ProductBatchDtoValidator()
    {
        RuleFor(r => r.BatchNumber)
            .ValidateText(
                resourceName: ProjectResource.Batch_Number,
                maxLength: ProjectDomainConstValues.BatchNumberMaxLength
            );

        RuleFor(r => r.LifeCycleDate).SetValidator(new DateRangeDataValidator());

        When(
            r => !string.IsNullOrEmpty(r.Irc),
            () =>
                RuleFor(r => r.Irc!)
                    .ValidateText(
                        resourceName: DomainResource.Irc,
                        maxLength: DomainConstValues.ProductBatchIrcMaxLength
                    )
        );

        When(
            r => !string.IsNullOrEmpty(r.Gtin),
            () =>
                RuleFor(r => r.Gtin!)
                    .ValidateText(
                        resourceName: DomainResource.Gtin,
                        maxLength: DomainConstValues.ProductBatchGtinMaxLength
                    )
        );

        When(
            r => !string.IsNullOrEmpty(r.Uid),
            () =>
                RuleFor(r => r.Uid!)
                    .ValidateText(
                        resourceName: DomainResource.Uid,
                        maxLength: DomainConstValues.ProductBatchUidMaxLength
                    )
        );

        RuleFor(r => r.BatchType).MustBeValidEnumeration<IProductBatchDto, BatchType>(DomainResource.Batch_Type);
    }
}
