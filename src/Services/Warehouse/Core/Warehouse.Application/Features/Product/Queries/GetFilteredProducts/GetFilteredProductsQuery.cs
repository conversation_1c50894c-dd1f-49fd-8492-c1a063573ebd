using BuildingBlocks.CQRS.Queries;
using MainArchitecture.Application.Models.FilteringAndPagination;

namespace Warehouse.Application.Features.Product.Queries.GetFilteredProducts;

/// <summary>
/// This query is handled by the <see cref="GetFilteredProductsQueryHandler"/> class.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="GetFilteredProductsQuery"/> class.
/// </remarks>
/// <param name="Filter">The filter criteria to apply when retrieving companies.</param>
public sealed record GetFilteredProductsQuery(FilterDto Filter)
    : QueryBase<PaginatedResult<GetFilteredProductsQueryResponse>>;
