using BuildingBlocks.CQRS.Events.EventSourcing;
using Microsoft.Extensions.Logging;
using Warehouse.Domain.Aggregates.ProductAggregate.Events;

namespace Warehouse.Application.Features.Product.Events;

/// <summary>
/// Handles the <see cref="ProductBatchDeletedDomainEvent" /> domain event.
/// </summary>
/// <seealso cref="IDomainEventSourcingHandler{TDomainEvent}" />
/// <remarks>
/// Initializes a new instance of the <see cref="ProductBatchDeletedDomainEventHandler"/> class.
/// </remarks>
/// <param name="logger">The logger.</param>
internal sealed class ProductBatchDeletedDomainEventHandler(ILogger<ProductBatchDeletedDomainEventHandler> logger)
    : IDomainEventSourcingHandler<ProductBatchDeletedDomainEvent>
{
    /// <inheritdoc />
    public Task Handle(ProductBatchDeletedDomainEvent notification, CancellationToken cancellationToken)
    {
        ProductDomainEventTracing.LogDeleteProductBatchRequested(
            logger,
            productId: notification.EntityId,
            productBatchId: notification.ProductBatchId
        );

        // TODO: Implement the logic to handle the domain event.
        return Task.CompletedTask;
    }
}
