using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.Core.IdGenerator;
using BuildingBlocks.CQRS.Commands;
using MainArchitecture.Application.Contracts.Infrastructure;
using Warehouse.Domain.Aggregates.SupplierOrderAggregate.Data;
using Warehouse.Domain.Aggregates.SupplierOrderAggregate.Specifications;
using Warehouse.Domain.Resources;

namespace Warehouse.Application.Features.SupplierOrder.Commands.DeleteSupplierOrder;

/// <summary>
/// Handles the processing of the <see cref="DeleteSupplierOrderCommand"/> command.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="DeleteSupplierOrderCommandHandler"/> class.
/// </remarks>
/// <param name="supplierOrderRepository">The repository for accessing supplierOrder data.</param>
/// <param name="organizationAccessor">The service for accessing identity data.</param>
internal sealed class DeleteSupplierOrderCommandHandler(
    ISupplierOrderRepository supplierOrderRepository,
    IOrganizationAccessor organizationAccessor
) : ICommandHandler<DeleteSupplierOrderCommand, bool>
{
    /// <inheritdoc />
    public async Task<Result<bool>> Handle(DeleteSupplierOrderCommand request, CancellationToken cancellationToken)
    {
        var spec = new SupplierOrderByIdAndOwnerSpec(
            request.SupplierOrderId,
            ownerId: organizationAccessor.GetOrganizationOwnership?.OwnerId ?? IdGenerator.EmptyId,
            isReadOnly: false
        );
        var supplierOrder = await supplierOrderRepository.FirstOrDefaultAsync(spec, cancellationToken);
        if (supplierOrder is null)
        {
            return SharedErrorMessages.NotFoundErrorMessage(DomainResource.Supplier_Order);
        }

        supplierOrder.DeleteApproval();

        supplierOrderRepository.Delete(supplierOrder);
        return await supplierOrderRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
    }
}
