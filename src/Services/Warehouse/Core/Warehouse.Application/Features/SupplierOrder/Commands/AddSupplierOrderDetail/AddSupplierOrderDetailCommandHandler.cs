using AutoMapper;
using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.Core.IdGenerator;
using BuildingBlocks.CQRS.Commands;
using BuildingBlocks.Security.Jwt.Services;
using MainArchitecture.Application.Contracts.Infrastructure;
using MainArchitecture.Domain.SharedKernel.ValueObjects;
using Warehouse.Application.Features.Product.Factories;
using Warehouse.Domain.Aggregates.SupplierOrderAggregate.Data;
using Warehouse.Domain.Aggregates.SupplierOrderAggregate.Specifications;
using Warehouse.Domain.Resources;

namespace Warehouse.Application.Features.SupplierOrder.Commands.AddSupplierOrderDetail;

/// <summary>
/// Handles the processing of the <see cref="AddSupplierOrderDetailCommand"/> command.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="AddSupplierOrderDetailCommandHandler"/> class.
/// </remarks>
/// <param name="supplierOrderRepository">The repository for accessing supplierOrder data.</param>
/// <param name="organizationAccessor">The service for accessing organization data.</param>
/// <param name="userAccessor">The service for accessing user information.</param>
/// <param name="mapper">The mapper.</param>
/// <param name="productRequirementDatabaseQuery">The product requirement database query.</param>
/// <param name="productQuantityComputationDatabaseQuery">The product quantity computation database query.</param>
internal sealed class AddSupplierOrderDetailCommandHandler(
    ISupplierOrderRepository supplierOrderRepository,
    IOrganizationAccessor organizationAccessor,
    IUserAccessor userAccessor,
    IMapper mapper,
    IProductRequirementDatabaseQuery productRequirementDatabaseQuery,
    IProductQuantityComputationDatabaseQuery productQuantityComputationDatabaseQuery
) : ICommandHandler<AddSupplierOrderDetailCommand, Guid>
{
    /// <inheritdoc />
    public async Task<Result<Guid>> Handle(AddSupplierOrderDetailCommand request, CancellationToken cancellationToken)
    {
        var ownerId = organizationAccessor.GetOrganizationOwnership?.OwnerId ?? IdGenerator.EmptyId;
        var spec = new SupplierOrderByIdAndOwnerWithDetailsSpec(
            request.SupplierOrderId,
            ownerId: ownerId,
            isReadOnly: false
        );
        var supplierOrder = await supplierOrderRepository.FirstOrDefaultAsync(spec, cancellationToken);
        if (supplierOrder is null)
        {
            return SharedErrorMessages.NotFoundErrorMessage(DomainResource.Supplier_Order);
        }

        var productRequirement = await ProductRequirementFactory.Create(
            request.ProductId,
            productRequirementDatabaseQuery,
            mapper
        );
        if (productRequirement.IsFailure)
        {
            return productRequirement.Errors!.FirstOrDefault()!;
        }

        var productQuantity = mapper.Map<ProductQuantity>(request.ProductQuantity);
        var productQuantityComputationResult = await ProductQuantityComputationFactory.Create(
            productQuantityComputationDatabaseQuery,
            request.ProductId,
            productQuantity
        );
        if (productQuantityComputationResult.IsFailure)
        {
            return productQuantityComputationResult.Errors!.First();
        }

        var loadedVersion = supplierOrder.Version;
        supplierOrder.AddDetail(
            productId: request.ProductId,
            productQuantityComputation: productQuantityComputationResult.Value!,
            authorId: userAccessor.GetUserIdentity,
            productRequirement: productRequirement.Value!
        );

        await supplierOrderRepository.ConcurrencySafeUpdate(supplierOrder, loadedVersion, cancellationToken);
        await supplierOrderRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);

        return supplierOrder.Details.Last().Id.Value;
    }
}
