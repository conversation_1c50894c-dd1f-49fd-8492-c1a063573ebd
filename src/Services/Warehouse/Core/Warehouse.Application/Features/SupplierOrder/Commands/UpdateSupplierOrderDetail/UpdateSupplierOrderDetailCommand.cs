using BuildingBlocks.CQRS.Commands;
using MainArchitecture.Application.Models.Common;
using Warehouse.Application.Features.SupplierOrder.Models.Dto;

namespace Warehouse.Application.Features.SupplierOrder.Commands.UpdateSupplierOrderDetail;

/// <summary>
/// This command is handled by <see cref="UpdateSupplierOrderDetailCommandHandler"/> class.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="UpdateSupplierOrderDetailCommand"/> class.
/// </remarks>
/// <param name="SupplierOrderDetailId">The ID of the supplier order detail to update.</param>
/// <param name="ProductId">The ID of the product to update.</param>
/// <param name="ProductQuantity">The quantity of the product to update.</param>
public sealed record UpdateSupplierOrderDetailCommand(
    Guid SupplierOrderDetailId,
    Guid ProductId,
    ProductQuantityData ProductQuantity
) : IdempotentCommand<bool>, ISupplierOrderDetailDto;
