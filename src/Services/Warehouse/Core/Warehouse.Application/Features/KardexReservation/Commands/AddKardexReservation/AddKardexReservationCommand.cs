using BuildingBlocks.CQRS.Commands;
using MainArchitecture.Application.Models.Common;
using MainArchitecture.Application.Models.Organization;

namespace Warehouse.Application.Features.KardexReservation.Commands.AddKardexReservation;

/// <summary>
/// This command is handled by <see cref="AddKardexReservationCommandHandler"/> class.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="AddKardexReservationCommand"/> class.
/// </remarks>
/// <param name="ActionDetailId">The action detail identifier.</param>
/// <param name="ActionId">The action identifier.</param>
/// <param name="WarehouseId">The warehouse identifier.</param>
/// <param name="ProductId">The product identifier.</param>
/// <param name="BatchInformation">The batch information.</param>
/// <param name="CompanyBranch">The company branch.</param>
/// <param name="Quantity">The quantity.</param>
/// <param name="OwnerId">The owner identifier.</param>
public sealed record AddKardexReservationCommand(
    Guid ActionDetailId,
    Guid ActionId,
    Guid WarehouseId,
    Guid ProductId,
    ProductBatchInformationData? BatchInformation,
    CompanyBranchData CompanyBranch,
    float Quantity,
    Guid OwnerId
) : InternalCommandBase<bool>;
