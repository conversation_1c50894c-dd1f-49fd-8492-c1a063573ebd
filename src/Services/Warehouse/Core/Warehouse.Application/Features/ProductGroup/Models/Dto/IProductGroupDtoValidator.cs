using BuildingBlocks.Core.Resources;
using BuildingBlocks.Validation.Extensions;
using FluentValidation;
using Warehouse.Domain.Constants;

namespace Warehouse.Application.Features.ProductGroup.Models.Dto;

/// <summary>
/// Validates the <see cref="IProductGroupDto"/> before it's processed by command validators.
/// </summary>
internal sealed class ProductGroupDtoValidator : AbstractValidator<IProductGroupDto>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="ProductGroupDtoValidator"/> class.
    /// </summary>
    public ProductGroupDtoValidator()
    {
        RuleFor(r => r.Name)
            .ValidateText(resourceName: SharedResource.Name, maxLength: DomainConstValues.ProductGroupNameMaxLength);

        RuleFor(r => r.Code)
            .ValidateText(resourceName: SharedResource.Code, maxLength: DomainConstValues.ProductGroupCodeMaxLength);
    }
}
