using BuildingBlocks.Core.Common.Maybe;
using BuildingBlocks.Core.IdGenerator;
using BuildingBlocks.CQRS.Queries;
using MainArchitecture.Application.Contracts.Infrastructure;
using MainArchitecture.Application.Models.FilteringAndPagination;

namespace Warehouse.Application.Features.ProductGroup.Queries.GetFilteredProductGroups;

/// <summary>
/// Handles the processing of the <see cref="GetFilteredProductGroupsQuery"/> query.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="GetFilteredProductGroupsQueryHandler"/> class.
/// </remarks>
/// <param name="databaseQuery">The database query for retrieving filtered product groups.</param>
/// <param name="organizationAccessor">The accessor for the current organization.</param>
internal sealed class GetFilteredProductGroupsQueryHandler(
    IGetFilteredProductGroupsDatabaseQuery databaseQuery,
    IOrganizationAccessor organizationAccessor
) : IQueryHandler<GetFilteredProductGroupsQuery, PaginatedResult<GetFilteredProductGroupsQueryResponse>>
{
    /// <inheritdoc />
    public async Task<Maybe<PaginatedResult<GetFilteredProductGroupsQueryResponse>>> Handle(
        GetFilteredProductGroupsQuery request,
        CancellationToken cancellationToken
    )
    {
        var ownerId = organizationAccessor.GetOrganizationOwnership?.OwnerId ?? IdGenerator.EmptyId;
        var result = await databaseQuery.Handle(request, ownerId, cancellationToken);
        var (productGroups, totalItems) = result.Value;
        return PaginatedResult<GetFilteredProductGroupsQueryResponse>.Create(
            source: productGroups,
            totalItems,
            request.Filter.PageSize
        );
    }
}
