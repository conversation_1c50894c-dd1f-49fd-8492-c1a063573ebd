using MainArchitecture.Application.Models.Organization;

namespace Warehouse.Application.Features.Supplier.Models.Dto;

/// <summary>
/// Represents the supplier person data transfer object.
/// </summary>
/// <param name="PersonId">The ID of the person associated with the personnel.</param>
/// <param name="FullName">The full name of the person associated with the personnel.</param>
public record SupplierPersonDto(Guid PersonId, string FullName) : IPersonDataReplicationDto;
