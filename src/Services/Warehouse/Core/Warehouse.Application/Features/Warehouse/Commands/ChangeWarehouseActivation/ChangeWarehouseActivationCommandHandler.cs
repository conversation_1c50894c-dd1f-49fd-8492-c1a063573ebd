using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.Core.IdGenerator;
using BuildingBlocks.CQRS.Commands;
using MainArchitecture.Application.Contracts.Infrastructure;
using Warehouse.Domain.Aggregates.WarehouseAggregate.Data;
using Warehouse.Domain.Aggregates.WarehouseAggregate.Specifications;
using Warehouse.Domain.Resources;

namespace Warehouse.Application.Features.Warehouse.Commands.ChangeWarehouseActivation;

/// <summary>
/// Handles the processing of the <see cref="ChangeWarehouseActivationCommand"/> command.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="ChangeWarehouseActivationCommandHandler"/> class.
/// </remarks>
/// <param name="warehouseRepository">The repository for accessing warehouse data.</param>
/// <param name="organizationAccessor">The service for accessing identity data.</param>
internal sealed class ChangeWarehouseActivationCommandHandler(
    IWarehouseRepository warehouseRepository,
    IOrganizationAccessor organizationAccessor
) : ICommandHandler<ChangeWarehouseActivationCommand, bool>
{
    /// <inheritdoc />
    public async Task<Result<bool>> Handle(
        ChangeWarehouseActivationCommand request,
        CancellationToken cancellationToken
    )
    {
        var spec = new WarehouseByIdAndOwnerSpec(
            warehouseId: request.WarehouseId,
            ownerId: organizationAccessor.GetOrganizationOwnership?.OwnerId ?? IdGenerator.EmptyId,
            isReadOnly: false
        );
        var currentWarehouse = await warehouseRepository.FirstOrDefaultAsync(spec, cancellationToken);
        if (currentWarehouse is null)
        {
            return SharedErrorMessages.NotFoundErrorMessage(DomainResource.Warehouse);
        }

        currentWarehouse.ChangeActivation();

        warehouseRepository.Update(currentWarehouse);
        return await warehouseRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
    }
}
