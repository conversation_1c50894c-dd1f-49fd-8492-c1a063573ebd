using BuildingBlocks.DDD.SeedWork.Data;
using Warehouse.Domain.Aggregates.OrganizationConfigurationAggregate.Entities;
using Warehouse.Domain.Aggregates.OrganizationConfigurationAggregate.ValueObjects;

namespace Warehouse.Domain.Aggregates.OrganizationConfigurationAggregate.Data;

/// <summary>
/// Represents a repository interface for the <see cref="OrganizationConfiguration"/> aggregate.
/// </summary>
public interface IOrganizationConfigurationRepository
    : IRepository<OrganizationConfiguration, OrganizationConfigurationId>;
