using BuildingBlocks.DDD.SeedWork.Primitives;

namespace Warehouse.Domain.Aggregates.KardexReservationAggregate.ValueObjects;

/// <summary>
/// Represents a kardex reservation identifier.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="KardexReservationId"/> class.
/// </remarks>
/// <param name="Value">The value of the kardex reservation identifier.</param>
public sealed record KardexReservationId(Guid Value) : TypedIdValueBase(Value)
{
    /// <summary>
    /// Initializes a new instance of the <see cref="KardexReservationId"/> class.
    /// </summary>
    /// <param name="value">The value.</param>
    /// <returns>The <see cref="KardexReservationId"/> kardex reservation identifier.</returns>
    public static KardexReservationId Create(Guid value) => new(value);

    /// <summary>
    /// Implicitly converts the kardex reservation identifier to a guid.
    /// </summary>
    /// <param name="kardexReservationId">The kardex reservation identifier.</param>
    public static implicit operator Guid(KardexReservationId kardexReservationId) => kardexReservationId.Value;

    /// <summary>
    /// Implicitly converts the guid to a kardex reservation identifier.
    /// </summary>
    /// <param name="kardexReservationId">The kardex reservation identifier.</param>
    public static implicit operator KardexReservationId(Guid kardexReservationId) => Create(kardexReservationId);
}
