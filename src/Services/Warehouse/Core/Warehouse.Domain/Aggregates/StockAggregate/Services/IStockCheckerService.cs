using BuildingBlocks.Core.Common.Maybe;
using BuildingBlocks.DDD.SeedWork.Services;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds.Warehouse;
using MainArchitecture.Domain.SharedKernel.ValueObjects;
using MainArchitecture.Domain.SharedKernel.ValueObjects.Organization;
using Warehouse.Domain.Aggregates.WarehouseAggregate.ValueObjects;

namespace Warehouse.Domain.Aggregates.StockAggregate.Services;

/// <summary>
/// Checks the availability of stock.
/// </summary>
public interface IStockCheckerService : IDomainService
{
    /// <summary>
    /// Checks if the salable stock is available.
    /// </summary>
    /// <param name="warehouseId">The warehouse identifier.</param>
    /// <param name="companyBranch">The company branch.</param>
    /// <param name="productId">The product identifier.</param>
    /// <param name="batchInformation">The batch information.</param>
    /// <param name="quantity">The quantity to check.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>True if the stock is available, otherwise false.</returns>
    Task<Maybe<bool>> IsSalableStockAvailableAsync(
        WarehouseId warehouseId,
        CompanyBranch companyBranch,
        ProductId productId,
        ProductBatchInformation? batchInformation,
        float quantity,
        CancellationToken cancellationToken = default
    );

    /// <summary>
    /// Checks if the stock is available.
    /// </summary>
    /// <param name="warehouseId">The warehouse identifier.</param>
    /// <param name="companyBranch">The company branch.</param>
    /// <param name="productId">The product identifier.</param>
    /// <param name="batchInformation">The batch information.</param>
    /// <param name="quantity">The quantity to check.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>True if the stock is available, otherwise false.</returns>
    Task<Maybe<bool>> IsStockAvailableAsync(
        WarehouseId warehouseId,
        CompanyBranch companyBranch,
        ProductId productId,
        ProductBatchInformation? batchInformation,
        float quantity,
        CancellationToken cancellationToken = default
    );
}
