using BuildingBlocks.Tests.IntegrationTests.Auth;
using BuildingBlocks.Tests.IntegrationTests.Extensions;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Web.Problems;
using FluentAssertions;
using MainArchitecture.Domain.Resources;
using MainArchitecture.Domain.SharedKernel.Enumerations;
using Warehouse.Application.Features.Product.Commands.AddProduct;
using Warehouse.EndToEndTests.Abstractions;
using Warehouse.Persistence.Persistence;
using Warehouse.TestShared.Fakes.Product.Requests;
using Warehouse.TestShared.Helpers;
using Xunit.Abstractions;

namespace Warehouse.EndToEndTests.Endpoints.Product.AddProduct;

public class AddProductEndpointTests : WarehouseEndToEndTestBase
{
    private readonly bool _isOwner;
    private readonly AddProductCommand _command;
    private readonly string _mainRoute;

    public AddProductEndpointTests(
        SharedFixtureWithEfCore<API.Program, WarehouseContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        _isOwner = SharedFixture.Faker.Random.Bool();
        _command = new FakeAddProductCommand().Generate();
        _mainRoute = Constants.Routes.Product.AddProduct;
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Add_WhenRequestIsValid_ShouldReturnCreatedResult()
    {
        // Arrange
        ArrangeSuccessAllAuthFilters(_isOwner);
        var productGroup = await ProductGroupHelpers.InsertProductGroupAsync(
            SharedFixture,
            e2e: true,
            cancellationToken: CancellationToken
        );
        var supplier = await SupplierHelpers.InsertSupplierAsync(
            SharedFixture,
            e2e: true,
            cancellationToken: CancellationToken
        );
        var brand = await BrandHelpers.InsertBrandAsync(SharedFixture, e2e: true, cancellationToken: CancellationToken);
        var command = _command with
        {
            AssignedProductGroups = [productGroup.Id],
            SupplierId = supplier.Id,
            BrandId = brand.Id,
        };

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: _isOwner)
            .PostAsJsonAsync(_mainRoute, command, cancellationToken: CancellationToken);

        // Assert
        response.Should().BeCreated<Guid>();
        await SharedFixture.AssertCacheAllAuthFilters(
            SystemPart.Product,
            AccessLevel.Create,
            _isOwner,
            cancellationToken: CancellationToken
        );
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Add_WhenCompanyBranchNotAssigned_ShouldReturnBusinessRuleBadRequestResult()
    {
        // Arrange
        ArrangeSuccessAllAuthFilters(_isOwner);
        var command = _command with { AssignedCompanyBranches = [] };

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: _isOwner)
            .PostAsJsonAsync(_mainRoute, command, cancellationToken: CancellationToken);

        // Assert
        response.Should().BeAgainstBusinessRule(ProjectResource.Company_Branch_Assignment_Limit_Error_Message);
        await SharedFixture.AssertCacheAllAuthFilters(
            SystemPart.Product,
            AccessLevel.Create,
            _isOwner,
            cancellationToken: CancellationToken
        );
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Add_WhenRequestIsNotValid_ShouldReturnValidationBadRequestResult()
    {
        // Arrange
        ArrangeSuccessAllAuthFilters(_isOwner);

        var command = new FakeInvalidAddProductCommand().Generate();

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: _isOwner)
            .PostAsJsonAsync(_mainRoute, command, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotBeValid();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Add_WhenUserIsNotAuthorized_ShouldReturnUnauthorizedResult()
    {
        // Arrange
        var httpClient = SharedFixture.GuestClient;

        // Act
        var response = await httpClient.PostAsJsonAsync(_mainRoute, _command, cancellationToken: CancellationToken);

        // Assert
        response.Should().Be401Unauthorized();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Add_WhenOrganizationNotSpecified_ShouldReturnForbiddenResult()
    {
        // Arrange
        var httpClient = SharedFixture.NormalUserHttpClient.WithRequestId();

        // Act
        var response = await httpClient.PostAsJsonAsync(_mainRoute, _command, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotHaveOrganization();
    }

    [Theory]
    [MemberData(nameof(InvalidNotOwnedOrganizationData))]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Add_WhenOrganizationInfoIsNotValid_ShouldReturnForbiddenResult(OrganizationValidationData data)
    {
        // Arrange
        var httpClient = SharedFixture.NormalUserHttpClient.AttachHttpClientHeaders(isOwner: data.IsOwner);
        SetOrganizationOwnershipAccess(hasValue: data.HasValue, isOwner: data.IsOwner, owner: data.OwnerId);

        // Act
        var response = await httpClient.PostAsJsonAsync(_mainRoute, _command, cancellationToken: CancellationToken);

        // Assert
        response.Should().HasProblemDetail(new ForbiddenProblemDetails(detail: data.Error)).And.Be403Forbidden();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Add_WhenDoesNotHaveMembership_ShouldReturnForbiddenResult()
    {
        // Arrange
        ArrangeFailedMembershipAuthFilters(_isOwner);

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: _isOwner)
            .PostAsJsonAsync(_mainRoute, _command, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotHaveMembership();
        await SharedFixture.AssertCacheInvalidMembership(_isOwner, CancellationToken);
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Add_WhenUserDoesNotHavePermission_ShouldReturnForbiddenResult()
    {
        // Arrange
        ArrangedFailedPermissionAuthFilters();

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: false)
            .PostAsJsonAsync(_mainRoute, _command, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotHavePermission();
        await SharedFixture.AssertCacheInvalidPermission(SystemPart.Product, AccessLevel.Create, CancellationToken);
    }
}
