using BuildingBlocks.Tests.IntegrationTests.Auth;
using BuildingBlocks.Tests.IntegrationTests.Extensions;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Web.Problems;
using FluentAssertions;
using MainArchitecture.Application.Models.FilteringAndPagination;
using MainArchitecture.Domain.SharedKernel.Enumerations;
using MainArchitecture.Infrastructure.Extensions;
using MainArchitecture.TestShared.Fakes.Common.Models;
using Warehouse.API.Endpoints.WarehouseType;
using Warehouse.Application.Features.WarehouseType.Queries.GetFilteredWarehouseTypes;
using Warehouse.EndToEndTests.Abstractions;
using Warehouse.Persistence.Persistence;
using Warehouse.TestShared.Helpers;
using Xunit.Abstractions;

namespace Warehouse.EndToEndTests.Endpoints.WarehouseType.GetFilteredWarehouseTypes;

/// <summary>
/// Represents the tests for the <see cref="WarehouseTypeEndpoints"/> class.
/// </summary>
public class GetFilteredWarehouseTypesEndpointTests : WarehouseEndToEndTestBase
{
    #region Constructor

    private const int TotalItems = 2;
    private readonly bool _isOwner;
    private readonly string _mainRoute;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetFilteredWarehouseTypesEndpointTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public GetFilteredWarehouseTypesEndpointTests(
        SharedFixtureWithEfCore<API.Program, WarehouseContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        _isOwner = SharedFixture.Faker.Random.Bool();
        var filter = new FakeFilterDto().Generate();
        _mainRoute = filter.UrlWithQueryParams(Constants.Routes.WarehouseType.GetFilteredWarehouseTypes);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenRequestIsValid_ShouldReturnOkResult()
    {
        // Arrange
        ArrangeSuccessAllAuthFilters(_isOwner);

        await WarehouseTypeHelpers.InsertWarehouseTypesAsync(
            SharedFixture,
            e2e: true,
            totalItems: TotalItems,
            cancellationToken: CancellationToken
        );

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: _isOwner)
            .GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response
            .Should()
            .BeOk<PaginatedResult<GetFilteredWarehouseTypesQueryResponse>>(res =>
            {
                res.Data.Should().NotBeEmpty();
                res.TotalItems.Should().Be(TotalItems);
            });

        await SharedFixture.AssertCacheAllAuthFilters(
            SystemPart.WarehouseType,
            AccessLevel.Get,
            _isOwner,
            cancellationToken: CancellationToken
        );
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenRequestIsNotValid_ShouldReturnValidationBadRequestResult()
    {
        // Arrange
        ArrangeSuccessAllAuthFilters(_isOwner);

        var filter = new FakeInvalidFilterDto().Generate();

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: _isOwner)
            .GetAsync(
                filter.UrlWithQueryParams(Constants.Routes.WarehouseType.GetFilteredWarehouseTypes),
                cancellationToken: CancellationToken
            );

        // Assert
        response.Should().NotBeValid();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenUserIsNotAuthorized_ShouldReturnUnauthorizedResult()
    {
        // Arrange
        var httpClient = SharedFixture.GuestClient;

        // Act
        var response = await httpClient.GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response.Should().Be401Unauthorized();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenOrganizationNotSpecified_ShouldReturnForbiddenResult()
    {
        // Arrange
        var httpClient = SharedFixture.NormalUserHttpClient.WithRequestId();

        // Act
        var response = await httpClient.GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotHaveOrganization();
    }

    [Theory]
    [MemberData(nameof(InvalidNotOwnedOrganizationData))]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenOrganizationInfoIsNotValid_ShouldReturnForbiddenResult(OrganizationValidationData data)
    {
        // Arrange
        var httpClient = SharedFixture.NormalUserHttpClient.AttachHttpClientHeaders(isOwner: data.IsOwner);
        SetOrganizationOwnershipAccess(hasValue: data.HasValue, isOwner: data.IsOwner, owner: data.OwnerId);

        // Act
        var response = await httpClient.GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response.Should().HasProblemDetail(new ForbiddenProblemDetails(detail: data.Error)).And.Be403Forbidden();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenDoesNotHaveMembership_ShouldReturnForbiddenResult()
    {
        // Arrange
        ArrangeFailedMembershipAuthFilters(_isOwner);

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: _isOwner)
            .GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotHaveMembership();
        await SharedFixture.AssertCacheInvalidMembership(_isOwner, CancellationToken);
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenUserDoesNotHavePermission_ShouldReturnForbiddenResult()
    {
        // Arrange
        ArrangedFailedPermissionAuthFilters();

        await WarehouseTypeHelpers.InsertWarehouseTypesAsync(
            SharedFixture,
            e2e: true,
            totalItems: TotalItems,
            cancellationToken: CancellationToken
        );

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: false)
            .GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotHavePermission();
        await SharedFixture.AssertCacheInvalidPermission(SystemPart.WarehouseType, AccessLevel.Get, CancellationToken);
    }
}
