using BuildingBlocks.Tests.Shared.XunitCategories;
using MainArchitecture.TestShared.Fakes.Organization.Events;
using Warehouse.Application.Features.OrganizationConfiguration.Commands.UpdateOrganizationConfigurationBranchDataReplication;
using Warehouse.Infrastructure.Consumers;
using Warehouse.UnitTests.Abstractions;

namespace Warehouse.UnitTests.Consumers;

/// <summary>
/// Represents the test class for the <see cref="BranchUpdatedIntegrationEventConsumer"/> class.
/// </summary>
public class BranchUpdatedIntegrationEventConsumerTests : WarehouseUnitTestBase
{
    #region Constructor

    private readonly BranchUpdatedIntegrationEventConsumer _consumer;

    /// <summary>
    /// Initializes a new instance of the <see cref="BranchUpdatedIntegrationEventConsumerTests"/> class.
    /// </summary>
    public BranchUpdatedIntegrationEventConsumerTests(WarehouseUnitTestFixture fixture)
        : base(fixture)
    {
        _consumer = new BranchUpdatedIntegrationEventConsumer(Fixture.Sender, Fixture.Mapper);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Consume_WhenValidCommandReceived_ShouldSendUpdateBranchCommand()
    {
        // Arrange
        var integrationCommand = new FakeBranchUpdatedIntegrationEvent().Generate();
        var consumeContext = CreateConsumeContext(integrationCommand);

        // Act
        await _consumer.Consume(consumeContext);

        // Assert
        await AssertSenderCommandReceived<UpdateOrganizationConfigurationBranchDataReplicationCommand>(Fixture.Sender);
    }
}
