using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Core.Common.Maybe;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.Tests.Shared.XunitCategories;
using MainArchitecture.Application.Configuration.Errors;
using MainArchitecture.Application.Models.FilteringAndPagination;
using MainArchitecture.TestShared.Fakes.Common.Models;
using Microsoft.AspNetCore.Http;
using Warehouse.API.Endpoints.Supplier;
using Warehouse.Application.Features.Supplier.Commands.AddSupplier;
using Warehouse.Application.Features.Supplier.Commands.ChangeSupplierActivation;
using Warehouse.Application.Features.Supplier.Commands.DeleteSupplier;
using Warehouse.Application.Features.Supplier.Commands.ManageSupplierPersonsAssignment;
using Warehouse.Application.Features.Supplier.Commands.UpdateSupplier;
using Warehouse.Application.Features.Supplier.Queries.GetFilteredSuppliers;
using Warehouse.Application.Features.Supplier.Queries.GetSupplierById;
using Warehouse.Application.Features.Supplier.Queries.GetSupplierPersonsBySupplierId;
using Warehouse.Domain.Resources;
using Warehouse.TestShared.Fakes.Supplier.Requests;
using Warehouse.UnitTests.Abstractions;

namespace Warehouse.UnitTests.Supplier.Endpoints;

/// <summary>
/// Represents the unit tests for the supplier endpoints.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="SupplierEndpointsTests"/> class.
/// </remarks>
public class SupplierEndpointsTests(WarehouseUnitTestFixture fixture) : WarehouseUnitTestBase(fixture)
{
    #region Fields

    /// <summary>
    /// Gets the filtered supplier dto.
    /// </summary>
    /// <returns>The filtered supplier dto.</returns>
    private static readonly FilterDtoWithSoftDelete _filterDto = new FakeFilterDtoWithSoftDelete().Generate();

    /// <summary>
    /// Gets the supplier by id query request.
    /// </summary>
    /// <returns>The supplier by id query request.</returns>
    private static readonly GetSupplierPersonsBySupplierIdQueryRequest _getSupplierPersonBySupplierIdRequest =
        new FakeGetSupplierPersonsBySupplierIdQueryRequest().Generate();

    /// <summary>
    /// Gets the supplier by id query request.
    /// </summary>
    /// <returns>The supplier by id query request.</returns>
    private static readonly GetSupplierByIdQueryRequest _getSupplierByIdRequest =
        new FakeGetSupplierByIdQueryRequest().Generate();

    /// <summary>
    /// Gets the add supplier command.
    /// </summary>
    /// <returns>The add supplier command.</returns>
    private static readonly AddSupplierCommand _addSupplierCommand = new FakeAddSupplierCommand().Generate();

    private static readonly UpdateSupplierCommand _updateSupplierCommand = new FakeUpdateSupplierCommand().Generate();

    /// <summary>
    /// Gets the change supplier activation command request.
    /// </summary>
    /// <returns>The change supplier activation command request.</returns>
    private static readonly ChangeSupplierActivationCommandRequest _changeSupplierActivationRequest =
        new FakeChangeSupplierActivationCommandRequest().Generate();

    /// <summary>
    /// Manage supplier person assignment command.
    /// </summary>
    /// <returns>The manage supplier person assignment command.</returns>
    private static readonly ManageSupplierPersonsAssignmentCommand _manageSupplierPersonAssignmentsCommand =
        new FakeManageSupplierPersonsAssignmentCommand().Generate();

    /// <summary>
    /// Gets the delete supplier command request.
    /// </summary>
    /// <returns>The delete supplier command request.</returns>
    private static readonly DeleteSupplierCommandRequest _deleteSupplierRequest =
        new FakeDeleteSupplierCommandRequest().Generate();

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task GetFilteredSuppliers_WhenDataIsValid_ShouldReturnOkResult()
    {
        // Arrange
        var data = new FakeGetFilteredSuppliersQueryResponse().Generate(2);
        var response = new FakePaginatedResult<GetFilteredSuppliersQueryResponse>()
            .WithData(data)
            .WithPageSize(_filterDto.PageSize)
            .Generate();
        var maybe = Maybe<PaginatedResult<GetFilteredSuppliersQueryResponse>>.From(response);
        ArrangeQuery(Fixture.Sender, maybe);

        // Act
        var result = await SupplierEndpoints.GetFilteredSuppliers(Fixture.BaseEndpointServices, _filterDto);

        // Assert
        await AssertOkResultAsync(Fixture.Sender, result, response, isQuery: true);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task GetFilteredSuppliers_WhenResultIsNull_ShouldReturnBadRequestResult()
    {
        // Arrange
        var maybe = Maybe<PaginatedResult<GetFilteredSuppliersQueryResponse>>.None;
        ArrangeQuery(Fixture.Sender, maybe);

        // Act
        var result = await SupplierEndpoints.GetFilteredSuppliers(Fixture.BaseEndpointServices, _filterDto);

        // Assert
        await AssertProblemResultAsync(Fixture.Sender, result, StatusCodes.Status400BadRequest, isQuery: true);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task GetSupplierById_WhenDataIsValid_ShouldReturnOkResult()
    {
        // Arrange
        var response = new FakeGetSupplierByIdQueryResponse().Generate();
        var maybe = Maybe<GetSupplierByIdQueryResponse>.From(response);
        ArrangeQuery(Fixture.Sender, maybe);

        // Act
        var result = await SupplierEndpoints.GetSupplierById(Fixture.BaseEndpointServices, _getSupplierByIdRequest);

        // Assert
        await AssertOkResultAsync(Fixture.Sender, result, response, isQuery: true);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task GetSupplierById_WhenResultIsNull_ShouldReturnNotFoundResult()
    {
        // Arrange
        var appError = SharedErrorMessages.NotFoundErrorMessage(DomainResource.Supplier);
        var maybe = Maybe<GetSupplierByIdQueryResponse>.FromError(appError);
        ArrangeQuery(Fixture.Sender, maybe);

        // Act
        var result = await SupplierEndpoints.GetSupplierById(Fixture.BaseEndpointServices, _getSupplierByIdRequest);

        // Assert
        await AssertProblemResultAsync(Fixture.Sender, result, StatusCodes.Status404NotFound, isQuery: true, appError);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task GetSupplierPersonsBySupplierId_WhenDataIsValid_ShouldReturnOkResult()
    {
        // Arrange
        var response = new List<GetSupplierPersonsBySupplierIdQueryResponse>
        {
            new FakeGetSupplierPersonsBySupplierIdQueryResponse().Generate(),
        };
        var maybe = Maybe<IReadOnlyCollection<GetSupplierPersonsBySupplierIdQueryResponse>>.From(response);
        ArrangeQuery(Fixture.Sender, maybe);

        // Act
        var result = await SupplierEndpoints.GetSupplierPersonsBySupplierId(
            Fixture.BaseEndpointServices,
            _getSupplierPersonBySupplierIdRequest
        );

        // Assert
        await AssertOkResultAsync(Fixture.Sender, result, response, isQuery: false);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task GetSupplierPersonsBySupplierId_WhenResultIsNull_ShouldReturnNotFoundResult()
    {
        // Arrange
        var appError = SharedErrorMessages.NotFoundErrorMessage(DomainResource.Supplier);
        var maybe = Maybe<IReadOnlyCollection<GetSupplierPersonsBySupplierIdQueryResponse>>.FromError(appError);
        ArrangeQuery(Fixture.Sender, maybe);

        // Act
        var result = await SupplierEndpoints.GetSupplierPersonsBySupplierId(
            Fixture.BaseEndpointServices,
            _getSupplierPersonBySupplierIdRequest
        );

        // Assert
        await AssertProblemResultAsync(Fixture.Sender, result, StatusCodes.Status404NotFound, isQuery: true, appError);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task AddSupplier_WhenDataIsValid_ShouldReturnCreatedResult()
    {
        // Arrange
        var supplierId = Faker.Random.Guid();
        var resultData = Result.Success(supplierId);
        ArrangeCommand(Fixture.Sender, resultData);

        // Act
        var result = await SupplierEndpoints.AddSupplier(Fixture.BaseEndpointServices, _addSupplierCommand);

        // Assert
        await AssertCreatedResultAsync<AddSupplierCommand, Guid>(Fixture.Sender, result, supplierId, isQuery: false);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task AddSupplier_WhenResultIsNull_ShouldReturnBadRequestResult()
    {
        // Arrange
        var appError = ProjectErrorMessages.OrganizationNotValid();
        var resultData = Result.Error<Guid>(appError);
        ArrangeCommand(Fixture.Sender, resultData);

        // Act
        var result = await SupplierEndpoints.AddSupplier(Fixture.BaseEndpointServices, _addSupplierCommand);

        // Assert
        await AssertProblemResultAsync(
            Fixture.Sender,
            result,
            StatusCodes.Status400BadRequest,
            isQuery: false,
            appError
        );
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task ManageSupplierPersonAssignments_WhenDataIsValid_ShouldReturnOkResult()
    {
        // Arrange
        var resultData = Result.Success(true);
        ArrangeCommand(Fixture.Sender, resultData);

        // Act
        var result = await SupplierEndpoints.ManageSupplierPersonsAssignment(
            Fixture.BaseEndpointServices,
            _manageSupplierPersonAssignmentsCommand
        );

        // Assert
        await AssertOkResultAsync(Fixture.Sender, result, true, isQuery: false);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task ManageSupplierPersonAssignments_WhenResultIsNull_ShouldReturnBadRequestResult()
    {
        // Arrange
        var appError = ProjectErrorMessages.OrganizationNotValid();
        var resultData = Result.Error<bool>(appError);
        ArrangeCommand(Fixture.Sender, resultData);

        // Act
        var result = await SupplierEndpoints.ManageSupplierPersonsAssignment(
            Fixture.BaseEndpointServices,
            _manageSupplierPersonAssignmentsCommand
        );

        // Assert
        await AssertProblemResultAsync(
            Fixture.Sender,
            result,
            StatusCodes.Status400BadRequest,
            isQuery: false,
            appError
        );
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task UpdateSupplier_WhenDataIsValid_ShouldReturnOkResult()
    {
        // Arrange
        const bool updated = true;
        var resultData = Result.Success(updated);
        ArrangeCommand(Fixture.Sender, resultData);

        // Act
        var result = await SupplierEndpoints.UpdateSupplier(Fixture.BaseEndpointServices, _updateSupplierCommand);

        // Assert
        await AssertOkResultAsync(Fixture.Sender, result, updated, isQuery: true);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task UpdateSupplier_WhenDataIsInvalid_ShouldReturnBadRequestResult()
    {
        // Arrange
        var appError = ProjectErrorMessages.OrganizationNotValid();
        var resultData = Result.Error<bool>(appError);
        ArrangeCommand(Fixture.Sender, resultData);

        // Act
        var result = await SupplierEndpoints.UpdateSupplier(Fixture.BaseEndpointServices, _updateSupplierCommand);

        // Assert
        await AssertProblemResultAsync(
            Fixture.Sender,
            result,
            StatusCodes.Status400BadRequest,
            isQuery: false,
            appError
        );
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task DeleteSupplier_WhenDataIsValid_ShouldReturnOkResult()
    {
        // Arrange
        const bool deleted = true;
        var resultData = Result.Success(deleted);
        ArrangeCommand(Fixture.Sender, resultData);

        // Act
        var result = await SupplierEndpoints.DeleteSupplier(Fixture.BaseEndpointServices, _deleteSupplierRequest);

        // Assert
        await AssertOkResultAsync(Fixture.Sender, result, deleted, isQuery: false);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task DeleteSupplier_WhenResultIsNull_ShouldReturnBadRequestResult()
    {
        // Arrange
        var appError = ProjectErrorMessages.OrganizationNotValid();
        var resultData = Result.Error<bool>(appError);
        ArrangeCommand(Fixture.Sender, resultData);

        // Act
        var result = await SupplierEndpoints.DeleteSupplier(Fixture.BaseEndpointServices, _deleteSupplierRequest);

        // Assert
        await AssertProblemResultAsync(
            Fixture.Sender,
            result,
            StatusCodes.Status400BadRequest,
            isQuery: false,
            appError
        );
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task ChangeSupplierActivation_WhenDataIsValid_ShouldReturnOkResult()
    {
        // Arrange
        const bool changed = true;
        var resultData = Result.Success(changed);
        ArrangeCommand(Fixture.Sender, resultData);

        // Act
        var result = await SupplierEndpoints.ChangeSupplierActivation(
            Fixture.BaseEndpointServices,
            _changeSupplierActivationRequest
        );

        // Assert
        await AssertOkResultAsync(Fixture.Sender, result, changed, isQuery: false);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task ChangeSupplierActivation_WhenResultIsNull_ShouldReturnBadRequestResult()
    {
        // Arrange
        var appError = ProjectErrorMessages.OrganizationNotValid();
        var resultData = Result.Error<bool>(appError);
        ArrangeCommand(Fixture.Sender, resultData);

        // Act
        var result = await SupplierEndpoints.ChangeSupplierActivation(
            Fixture.BaseEndpointServices,
            _changeSupplierActivationRequest
        );

        // Assert
        await AssertProblemResultAsync(
            Fixture.Sender,
            result,
            StatusCodes.Status400BadRequest,
            isQuery: false,
            appError
        );
    }
}
