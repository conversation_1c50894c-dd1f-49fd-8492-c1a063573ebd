using BuildingBlocks.Core.Exception.Types;
using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using MainArchitecture.Domain.SharedKernel.ValueObjects.Organization;
using Warehouse.Domain.Aggregates.WarehouseAggregate.Entities;

namespace Warehouse.UnitTests.Warehouse.Entities;

/// <summary>
/// Represents tests for the WarehouseCompanyBranch entity.
/// </summary>
public class WarehouseCompanyBranchTests : WarehouseCompanyBranchBaseTest
{
    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Create_WhenValidData_ShouldCreateWarehouseCompanyBranch()
    {
        // Arrange

        // Act
        var warehouseCompanyBranch = WarehouseCompanyBranch.Create(
            WarehouseCompanyBranch.CompanyBranch,
            WarehouseCompanyBranch.IsMain
        );

        // Assert
        warehouseCompanyBranch.Should().NotBeNull();
        warehouseCompanyBranch.CompanyBranch.Should().Be(WarehouseCompanyBranch.CompanyBranch);
        warehouseCompanyBranch.IsMain.Should().Be(WarehouseCompanyBranch.IsMain);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Create_WhenInvalidData_ShouldThrowException()
    {
        // Arrange
        CompanyBranch companyBranch = default!;

        // Act
        FluentActions
            .Invoking(() => WarehouseCompanyBranch.Create(companyBranch, IsMain))
            .Should()
            .Throw<CustomAppException>();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void MarkAsMain_WhenValidData_ShouldMarkAsMain()
    {
        // Arrange

        var warehouseCompanyBranch = WarehouseCompanyBranch.Create(CompanyBranch, false);

        // Act
        warehouseCompanyBranch.MarkAsMain();

        // Assert
        warehouseCompanyBranch.IsMain.Should().BeTrue();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void MarkAsNotMain_WhenValidData_ShouldMarkAsNotMain()
    {
        // Arrange
        var warehouseCompanyBranch = WarehouseCompanyBranch.Create(CompanyBranch, true);

        // Act
        warehouseCompanyBranch.MarkAsNotMain();

        // Assert
        warehouseCompanyBranch.IsMain.Should().BeFalse();
    }
}
