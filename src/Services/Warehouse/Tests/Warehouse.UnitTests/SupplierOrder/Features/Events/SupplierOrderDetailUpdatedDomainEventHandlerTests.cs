using BuildingBlocks.Tests.Shared.XunitCategories;
using Microsoft.Extensions.Logging.Abstractions;
using Warehouse.Application.Features.SupplierOrder.Events;
using Warehouse.TestShared.Fakes.SupplierOrder.Events;
using Warehouse.UnitTests.Abstractions;

namespace Warehouse.UnitTests.SupplierOrder.Features.Events;

/// <summary>
/// Represents the unit tests for the <see cref="SupplierOrderDetailUpdatedDomainEventHandler"/> class.
/// </summary>
public class SupplierOrderDetailUpdatedDomainEventHandlerTests : WarehouseUnitTestBase
{
    #region Constructor

    private readonly SupplierOrderDetailUpdatedDomainEventHandler _handler;

    /// <summary>
    /// Initializes a new instance of the <see cref="SupplierOrderDetailUpdatedDomainEventHandlerTests"/> class.
    /// </summary>
    public SupplierOrderDetailUpdatedDomainEventHandlerTests(WarehouseUnitTestFixture fixture)
        : base(fixture)
    {
        _handler = new SupplierOrderDetailUpdatedDomainEventHandler(
            NullLogger<SupplierOrderDetailUpdatedDomainEventHandler>.Instance
        );
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public Task Handle_WhenCalled_ShouldPublishSupplierOrderDetailUpdatedIntegrationEvent()
    {
        // Arrange
        var domainEvent = new FakeSupplierOrderDetailUpdatedDomainEvent().Generate();

        // Act
        return _handler.Handle(domainEvent, CancellationToken.None);
    }
}
