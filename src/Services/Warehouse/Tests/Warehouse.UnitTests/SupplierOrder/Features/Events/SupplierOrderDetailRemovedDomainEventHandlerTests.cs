using BuildingBlocks.Tests.Shared.XunitCategories;
using Microsoft.Extensions.Logging.Abstractions;
using Warehouse.Application.Features.SupplierOrder.Events;
using Warehouse.TestShared.Fakes.SupplierOrder.Events;
using Warehouse.UnitTests.Abstractions;

namespace Warehouse.UnitTests.SupplierOrder.Features.Events;

/// <summary>
/// Represents the unit tests for the <see cref="SupplierOrderDetailRemovedDomainEventHandler"/> class.
/// </summary>
public class SupplierOrderDetailRemovedDomainEventHandlerTests : WarehouseUnitTestBase
{
    #region Constructor

    private readonly SupplierOrderDetailRemovedDomainEventHandler _handler;

    /// <summary>
    /// Initializes a new instance of the <see cref="SupplierOrderDetailRemovedDomainEventHandlerTests"/> class.
    /// </summary>
    public SupplierOrderDetailRemovedDomainEventHandlerTests(WarehouseUnitTestFixture fixture)
        : base(fixture)
    {
        _handler = new SupplierOrderDetailRemovedDomainEventHandler(
            NullLogger<SupplierOrderDetailRemovedDomainEventHandler>.Instance
        );
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public Task Handle_WhenCalled_ShouldPublishSupplierOrderDetailRemovedIntegrationEvent()
    {
        // Arrange
        var domainEvent = new FakeSupplierOrderDetailRemovedDomainEvent().Generate();

        // Act
        return _handler.Handle(domainEvent, CancellationToken.None);
    }
}
