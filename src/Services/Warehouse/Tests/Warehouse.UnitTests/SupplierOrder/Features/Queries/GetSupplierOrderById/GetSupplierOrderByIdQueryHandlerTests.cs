using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using NSubstitute;
using Warehouse.Application.Features.SupplierOrder.Queries.GetSupplierOrderById;
using Warehouse.Domain.Resources;
using Warehouse.TestShared.Fakes.SupplierOrder.Requests;
using Warehouse.UnitTests.Abstractions;

namespace Warehouse.UnitTests.SupplierOrder.Features.Queries.GetSupplierOrderById;

/// <summary>
/// Represents the unit tests for the <see cref="GetSupplierOrderByIdQueryHandler"/> class.
/// </summary>
public class GetSupplierOrderByIdQueryHandlerTests : WarehouseUnitTestBase
{
    #region Constructor

    private readonly GetSupplierOrderByIdQueryHandler _handler;
    private readonly IGetSupplierOrderByIdDatabaseQuery _databaseQuery;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetSupplierOrderByIdQueryHandlerTests"/> class.
    /// </summary>
    public GetSupplierOrderByIdQueryHandlerTests(WarehouseUnitTestFixture fixture)
        : base(fixture)
    {
        _databaseQuery = Substitute.For<IGetSupplierOrderByIdDatabaseQuery>();
        _handler = new GetSupplierOrderByIdQueryHandler(_databaseQuery, Fixture.OrganizationAccessor);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenDataIsValid_ShouldReturnSupplierOrder()
    {
        // Arrange
        Fixture.SetupOrganizationAccessor();
        var query = new FakeGetSupplierOrderByIdQuery().Generate();
        var expectedResponse = new FakeGetSupplierOrderByIdQueryResponse().Generate();

        _databaseQuery.Handle(query, Arg.Any<Guid>(), Arg.Any<CancellationToken>()).Returns(expectedResponse);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.AssertFounded(expectedResponse);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenSupplierOrderNotFound_ShouldReturnNotFoundError()
    {
        // Arrange
        var query = new FakeGetSupplierOrderByIdQuery().Generate();

        _databaseQuery
            .Handle(query, Arg.Any<Guid>(), Arg.Any<CancellationToken>())
            .Returns(default(GetSupplierOrderByIdQueryResponse));

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.AssertNotFoundError(DomainResource.Supplier_Order);
    }
}
