using BuildingBlocks.Core.Common.Maybe;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using MainArchitecture.Application.Configuration.Errors;
using MainArchitecture.Application.Models.Organization;
using MainArchitecture.TestShared.Fakes.Organization.Models;
using NSubstitute;
using Warehouse.Application.Features.SupplierOrder.Queries.GetFilteredSupplierOrderDetails;
using Warehouse.TestShared.Fakes.SupplierOrder.Requests;
using Warehouse.UnitTests.Abstractions;

namespace Warehouse.UnitTests.SupplierOrder.Features.Queries.GetFilteredSupplierOrderDetails;

/// <summary>
/// Represents the unit tests for the <see cref="GetFilteredSupplierOrderDetailsQueryHandler"/> class.
/// </summary>
public class GetFilteredSupplierOrderDetailsQueryHandlerTests : WarehouseUnitTestBase
{
    #region Constructor

    private readonly GetFilteredSupplierOrderDetailsQueryHandler _handler;
    private readonly IGetFilteredSupplierOrderDetailsDatabaseQuery _databaseQuery;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetFilteredSupplierOrderDetailsQueryHandlerTests"/> class.
    /// </summary>
    public GetFilteredSupplierOrderDetailsQueryHandlerTests(WarehouseUnitTestFixture fixture)
        : base(fixture)
    {
        _databaseQuery = Substitute.For<IGetFilteredSupplierOrderDetailsDatabaseQuery>();
        _handler = new GetFilteredSupplierOrderDetailsQueryHandler(_databaseQuery, Fixture.OrganizationAccessor);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenCalled_ShouldReturnAssignedSupplierOrderDetails()
    {
        // Arrange
        Fixture.SetupOrganizationAccessor(companyBranch: new FakeCompanyBranchData().Generate());
        var query = new FakeGetFilteredSupplierOrderDetailsQuery().Generate();
        var items = new FakeGetFilteredSupplierOrderDetailsQueryResponse().Generate(10);
        var totalItems = Faker.Random.Int(items.Count, 100);
        var response = (items, totalItems);

        _databaseQuery
            .Handle(query, Arg.Any<Guid>(), Arg.Any<CompanyBranchData>(), Arg.Any<CancellationToken>())
            .Returns(
                Task.FromResult(
                    Maybe<(
                        IReadOnlyCollection<GetFilteredSupplierOrderDetailsQueryResponse> SupplierOrderDetails,
                        int TotalItems
                    )>.From(response)
                )
            );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.HasValue.Should().BeTrue();
        result.Value!.Data.Should().BeEquivalentTo(items);
        result.Value.TotalItems.Should().Be(totalItems);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenCompanyBranchNotSpecified_ShouldReturnError()
    {
        // Arrange
        Fixture.SetupOrganizationAccessor(companyBranch: null);
        var query = new FakeGetFilteredSupplierOrderDetailsQuery().Generate();
        var expectedResponse = (new List<GetFilteredSupplierOrderDetailsQueryResponse>(), 0);

        _databaseQuery
            .Handle(query, Arg.Any<Guid>(), Arg.Any<CompanyBranchData>(), Arg.Any<CancellationToken>())
            .Returns(
                Task.FromResult(
                    Maybe<(
                        IReadOnlyCollection<GetFilteredSupplierOrderDetailsQueryResponse> SupplierOrderDetails,
                        int TotalItems
                    )>.From(expectedResponse)
                )
            );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.AssertProblemMaybe();
        result.Error.Should().Be(ProjectErrorMessages.OrganizationNotSpecified());
    }
}
