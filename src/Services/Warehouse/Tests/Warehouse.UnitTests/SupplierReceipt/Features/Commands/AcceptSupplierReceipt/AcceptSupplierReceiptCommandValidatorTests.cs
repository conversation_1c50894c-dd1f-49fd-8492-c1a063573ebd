using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Tests.UnitTests.Abstractions;
using FluentValidation.TestHelper;
using Microsoft.Extensions.Logging.Abstractions;
using Warehouse.Application.Features.SupplierReceipt.Commands.AcceptSupplierReceipt;
using Warehouse.TestShared.Fakes.SupplierReceipt.Requests;

namespace Warehouse.UnitTests.SupplierReceipt.Features.Commands.AcceptSupplierReceipt;

/// <summary>
/// Represents the unit tests for the <see cref="AcceptSupplierReceiptCommandValidator"/> class.
/// </summary>
public class AcceptSupplierReceiptCommandValidatorTests : BaseApplicationUnitTest
{
    #region Constructor

    /// <summary>
    /// Gets the validator.
    /// </summary>
    private readonly AcceptSupplierReceiptCommandValidator _validator;

    /// <summary>
    /// Initializes a new instance of the <see cref="AcceptSupplierReceiptCommandValidatorTests"/> class.
    /// </summary>
    public AcceptSupplierReceiptCommandValidatorTests()
    {
        _validator = new AcceptSupplierReceiptCommandValidator(
            NullLogger<AcceptSupplierReceiptCommandValidator>.Instance
        );
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenValidInput_ShouldNotHaveValidationError()
    {
        // Arrange
        var command = new FakeAcceptSupplierReceiptCommand().Generate();

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenInvalidInput_ShouldHaveValidationError()
    {
        // Arrange
        var command = new FakeInvalidAcceptSupplierReceiptCommand().Generate();

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.SupplierReceiptId);
    }
}
