using BuildingBlocks.Core.Common.Maybe;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using NSubstitute;
using Warehouse.Application.Features.SupplierReceipt.Queries.GetSupplierReceiptDetailById;
using Warehouse.Domain.Resources;
using Warehouse.TestShared.Fakes.SupplierReceipt.Requests;
using Warehouse.UnitTests.Abstractions;

namespace Warehouse.UnitTests.SupplierReceipt.Features.Queries.GetSupplierReceiptDetailById;

/// <summary>
/// Represents the unit tests for the <see cref="GetSupplierReceiptDetailByIdQueryHandler"/> class.
/// </summary>
public class GetSupplierReceiptDetailByIdQueryHandlerTests : WarehouseUnitTestBase
{
    #region Constructor

    private readonly GetSupplierReceiptDetailByIdQueryHandler _handler;
    private readonly IGetSupplierReceiptDetailByIdDatabaseQuery _databaseQuery;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetSupplierReceiptDetailByIdQueryHandlerTests"/> class.
    /// </summary>
    public GetSupplierReceiptDetailByIdQueryHandlerTests(WarehouseUnitTestFixture fixture)
        : base(fixture)
    {
        _databaseQuery = Substitute.For<IGetSupplierReceiptDetailByIdDatabaseQuery>();
        _handler = new GetSupplierReceiptDetailByIdQueryHandler(_databaseQuery, Fixture.OrganizationAccessor);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenDataIsValid_ShouldReturnSupplierReceiptDetail()
    {
        // Arrange
        Fixture.SetupOrganizationAccessor();
        var query = new FakeGetSupplierReceiptDetailByIdQuery().Generate();
        var expectedResponse = new FakeGetSupplierReceiptDetailByIdQueryResponse().Generate();

        _databaseQuery
            .Handle(query, Arg.Any<Guid>(), Arg.Any<CancellationToken>())
            .Returns(Maybe<GetSupplierReceiptDetailByIdQueryResponse>.From(expectedResponse));

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.AssertFounded(expectedResponse);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenSupplierReceiptDetailNotFound_ShouldReturnNotFoundError()
    {
        // Arrange
        var query = new FakeGetSupplierReceiptDetailByIdQuery().Generate();

        _databaseQuery
            .Handle(query, Arg.Any<Guid>(), Arg.Any<CancellationToken>())
            .Returns(Maybe<GetSupplierReceiptDetailByIdQueryResponse>.None);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.AssertNotFoundError(DomainResource.Supplier_Receipt_Detail);
    }
}
