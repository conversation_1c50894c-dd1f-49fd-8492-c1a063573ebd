using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using FluentValidation.TestHelper;
using Microsoft.Extensions.Logging.Abstractions;
using Warehouse.Application.Features.Product.Queries.GetFilteredProductPrices;
using Warehouse.TestShared.Fakes.Product.Requests;

namespace Warehouse.UnitTests.Product.Features.Queries.GetFilteredProductPrices;

/// <summary>
/// Represents the unit tests for the <see cref="GetFilteredProductPricesQueryRequestValidator"/> class.
/// </summary>
public class GetFilteredProductPricesQueryRequestValidatorTests
{
    #region Constructor

    private readonly GetFilteredProductPricesQueryRequestValidator _validator;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetFilteredProductPricesQueryRequestValidatorTests"/> class.
    /// </summary>
    public GetFilteredProductPricesQueryRequestValidatorTests()
    {
        _validator = new GetFilteredProductPricesQueryRequestValidator(
            NullLogger<GetFilteredProductPricesQueryRequestValidator>.Instance
        );
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenValidInput_ShouldReturnTrue()
    {
        // Arrange
        var request = new FakeGetFilteredProductPricesQueryRequest().Generate();

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenInvalidInput_ShouldReturnFalse()
    {
        // Arrange
        var request = new FakeInvalidGetFilteredProductPricesQueryRequest().Generate();

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.ShouldHaveValidationErrorFor(x => x.ProductId);
    }
}
