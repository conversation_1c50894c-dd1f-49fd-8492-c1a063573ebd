using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using FluentValidation.TestHelper;
using Microsoft.Extensions.Logging.Abstractions;
using Warehouse.Application.Features.Product.Queries.GetProductById;
using Warehouse.TestShared.Fakes.Product.Requests;

namespace Warehouse.UnitTests.Product.Features.Queries.GetProductById;

/// <summary>
/// Represents the unit tests for the <see cref="GetProductByIdQueryRequestValidator"/> class.
/// </summary>
public class GetProductByIdQueryRequestValidatorTests
{
    #region Constructor

    private readonly GetProductByIdQueryRequestValidator _validator;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetProductByIdQueryRequestValidatorTests"/> class.
    /// </summary>
    public GetProductByIdQueryRequestValidatorTests()
    {
        _validator = new GetProductByIdQueryRequestValidator(NullLogger<GetProductByIdQueryRequestValidator>.Instance);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenValidInput_ShouldReturnTrue()
    {
        // Arrange
        var request = new FakeGetProductByIdQueryRequest().Generate();

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenInvalidInput_ShouldReturnFalse()
    {
        // Arrange
        var request = new FakeInvalidGetProductByIdQueryRequest().Generate();

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.ShouldHaveValidationErrorFor(x => x.ProductId);
    }
}
