using BuildingBlocks.Core.Types.Extensions;
using BuildingBlocks.DDD.SeedWork.Exceptions;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using MainArchitecture.Application.Models.Organization;
using MainArchitecture.Domain.Resources;
using MainArchitecture.Domain.SharedKernel.ValueObjects.Organization;
using MainArchitecture.TestShared.Fakes.Organization.Models;
using MainArchitecture.TestShared.Fakes.Organization.ValueObjects;
using Warehouse.API;
using Warehouse.Application.Features.SupplierReceipt.Commands.UpdateSupplierReceipt;
using Warehouse.Domain.Aggregates.SupplierReceiptAggregate.Events;
using Warehouse.Domain.Resources;
using Warehouse.Domain.SharedKernel.Enumerations;
using Warehouse.IntegrationTests.Abstractions;
using Warehouse.Persistence.Persistence;
using Warehouse.TestShared.Fakes.SupplierReceipt.Requests;
using Warehouse.TestShared.Helpers;
using Xunit.Abstractions;
using SupplierDomain = Warehouse.Domain.Aggregates.SupplierAggregate.Entities.Supplier;
using SupplierOrderDomain = Warehouse.Domain.Aggregates.SupplierOrderAggregate.Entities.SupplierOrder;
using WarehouseDomain = Warehouse.Domain.Aggregates.WarehouseAggregate.Entities.Warehouse;

namespace Warehouse.IntegrationTests.Features.SupplierReceipt.Commands.UpdateSupplierReceipt;

/// <summary>
///     Represents the tests for the <see cref="UpdateSupplierReceiptCommand" /> class.
/// </summary>
public class UpdateSupplierReceiptTests : WarehouseIntegrationTestBase
{
    #region Constructor

    private readonly UpdateSupplierReceiptCommand _command;

    /// <summary>
    ///     Initializes a new instance of the <see cref="UpdateSupplierReceiptTests" /> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public UpdateSupplierReceiptTests(
        SharedFixtureWithEfCore<Program, WarehouseContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        SharedFixture.SetUserAndOrganization();
        _command = new FakeUpdateSupplierReceiptCommand().Generate();
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenDataExistsWithOutOrder_ShouldUpdateSupplierReceipt()
    {
        // Arrange
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();
        var companyBranch = await SharedFixture.MapToCompanyBranchesAsync(companyBranchData);

        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            userId: ownerId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var warehouse = await InsertWarehouseAsync([companyBranch.First()]);
        var supplier = await InsertSupplierAsync([companyBranchData]);
        var command = _command with
        {
            SupplierReceiptId = supplierReceipt.Id,
            WarehouseId = warehouse.Id,
            SupplierId = supplier.Id,
        };

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertSuccessResult();
        response.Value.Should().BeTrue();

        var updatedSupplierReceipt = await SupplierReceiptHelpers.GetSupplierReceiptByIdAndOwnerAsync(
            SharedFixture,
            supplierReceipt.Id,
            ownerId,
            CancellationToken
        );
        updatedSupplierReceipt.Should().NotBeNull();
        updatedSupplierReceipt!.Id.Value.Should().Be(command.SupplierReceiptId);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenDataExistsWithOrder_ShouldUpdateSupplierReceipt()
    {
        // Arrange
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();
        var companyBranch = await SharedFixture.MapToCompanyBranchesAsync(companyBranchData);
        var supplierOrder = await InsertSupplierOrderAsync(companyBranchData);
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            userId: ownerId,
            companyBranch: companyBranchData,
            supplierOrderId: supplierOrder.Id,
            cancellationToken: CancellationToken
        );
        var warehouse = await InsertWarehouseAsync([companyBranch.First()]);
        var command = _command with { SupplierReceiptId = supplierReceipt.Id, WarehouseId = warehouse.Id };

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertSuccessResult();
        response.Value.Should().BeTrue();

        var updatedSupplierReceipt = await SupplierReceiptHelpers.GetSupplierReceiptByIdAndOwnerAsync(
            SharedFixture,
            supplierReceipt.Id,
            ownerId,
            CancellationToken
        );
        updatedSupplierReceipt.Should().NotBeNull();
        updatedSupplierReceipt!.Id.Value.Should().Be(command.SupplierReceiptId);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierReceiptNotFound_ShouldReturnNotFoundResult()
    {
        // Arrange

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertNotFoundError(DomainResource.Supplier_Receipt);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierReceiptAccepted_ShouldThrowBusinessRuleException()
    {
        // Arrange
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();
        var companyBranch = await SharedFixture.MapToCompanyBranchesAsync(companyBranchData);

        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            userId: ownerId,
            companyBranch: companyBranchData,
            stateId: WarehouseOperationState.Accepted,
            cancellationToken: CancellationToken
        );
        var warehouse = await InsertWarehouseAsync([companyBranch.First()]);
        var supplier = await InsertSupplierAsync([companyBranchData]);
        var command = _command with
        {
            SupplierReceiptId = supplierReceipt.Id,
            WarehouseId = warehouse.Id,
            SupplierId = supplier.Id,
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<BusinessRuleValidationException>()
            .WithMessage(ProjectResource.Operation_Already_Handled_Error_Message);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierReceiptSent_ShouldThrowBusinessRuleException()
    {
        // Arrange
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();
        var companyBranch = await SharedFixture.MapToCompanyBranchesAsync(companyBranchData);

        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            userId: ownerId,
            companyBranch: companyBranchData,
            stateId: WarehouseOperationState.Sent,
            cancellationToken: CancellationToken
        );
        var warehouse = await InsertWarehouseAsync([companyBranch.First()]);
        var supplier = await InsertSupplierAsync([companyBranchData]);
        var command = _command with
        {
            SupplierReceiptId = supplierReceipt.Id,
            WarehouseId = warehouse.Id,
            SupplierId = supplier.Id,
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<BusinessRuleValidationException>()
            .WithMessage(ProjectResource.Operation_Already_Handled_Error_Message);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierReceiptUpdated_ShouldPersistInTheOutbox()
    {
        // Arrange;
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();
        var companyBranch = await SharedFixture.MapToCompanyBranchesAsync(companyBranchData);

        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            userId: ownerId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var warehouse = await InsertWarehouseAsync([companyBranch.First()]);
        var supplier = await InsertSupplierAsync([companyBranchData]);
        var command = _command with
        {
            SupplierReceiptId = supplierReceipt.Id,
            WarehouseId = warehouse.Id,
            SupplierId = supplier.Id,
        };

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertSuccessResult();

        await SharedFixture.ShouldProcessedOutboxPersistMessage<SupplierReceiptUpdatedDomainEvent>(CancellationToken);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenWarehouseOrganizationDoNotSyncWithTargetOrganization_ShouldThrowBusinessRuleException()
    {
        // Arrange
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            userId: ownerId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var warehouse = await InsertWarehouseAsync(new FakeCompanyBranch().Generate(1));
        var supplier = await InsertSupplierAsync([companyBranchData]);
        var command = _command with
        {
            SupplierReceiptId = supplierReceipt.Id,
            WarehouseId = warehouse.Id,
            SupplierId = supplier.Id,
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<BusinessRuleValidationException>()
            .WithMessage(ProjectResource.Operation_Organization_Must_Sync_With_Target_Organization_Error_Message);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenWarehouseIsNotActive_ShouldThrowBusinessRuleException()
    {
        // Arrange
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();
        var companyBranch = await SharedFixture.MapToCompanyBranchesAsync(companyBranchData);

        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            userId: ownerId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var warehouse = await InsertWarehouseAsync([companyBranch.First()], false);
        var supplier = await InsertSupplierAsync([companyBranchData]);
        var command = _command with
        {
            SupplierReceiptId = supplierReceipt.Id,
            WarehouseId = warehouse.Id,
            SupplierId = supplier.Id,
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<BusinessRuleValidationException>()
            .WithMessage(ProjectResource.Target_Must_Be_Active_Error_Message.FormatWithStr("warehouse"));
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierOrganizationDoNotSyncWithTargetOrganization_ShouldThrowBusinessRuleException()
    {
        // Arrange
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();
        var companyBranch = await SharedFixture.MapToCompanyBranchesAsync(companyBranchData);

        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            userId: ownerId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var warehouse = await InsertWarehouseAsync([companyBranch.First()]);
        var supplier = await InsertSupplierAsync(new FakeCompanyBranchData().Generate(1));
        var command = _command with
        {
            SupplierReceiptId = supplierReceipt.Id,
            WarehouseId = warehouse.Id,
            SupplierId = supplier.Id,
        };
        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<BusinessRuleValidationException>()
            .WithMessage(ProjectResource.Operation_Organization_Must_Sync_With_Target_Organization_Error_Message);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierIsNotActive_ShouldThrowBusinessRuleException()
    {
        // Arrange
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();
        var companyBranch = await SharedFixture.MapToCompanyBranchesAsync(companyBranchData);

        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            userId: ownerId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var warehouse = await InsertWarehouseAsync([companyBranch.First()]);
        var supplier = await InsertSupplierAsync([companyBranchData], false);
        var command = _command with
        {
            SupplierReceiptId = supplierReceipt.Id,
            WarehouseId = warehouse.Id,
            SupplierId = supplier.Id,
        };
        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<BusinessRuleValidationException>()
            .WithMessage(ProjectResource.Target_Must_Be_Active_Error_Message.FormatWithStr(nameof(supplier)));
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenAlreadyCalledWithSameRequestId_ShouldBeIdempotent()
    {
        // Arrange
        await SharedFixture.SendAsync(_command, CancellationToken);

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertIdempotentResult();
    }

    #region Privates

    /// <summary>
    /// Inserts a supplier into the database.
    /// </summary>
    private async Task<SupplierDomain> InsertSupplierAsync(
        IReadOnlyCollection<CompanyBranchData>? companyBranch = null,
        bool isActive = true,
        CancellationToken cancellationToken = default
    )
    {
        var supplier = await SupplierHelpers.InsertSupplierAsync(
            SharedFixture,
            e2e: false,
            userId: null,
            companyBranch: companyBranch,
            isActive: isActive,
            cancellationToken: cancellationToken
        );
        return supplier;
    }

    /// <summary>
    /// Inserts a warehouse into the database.
    /// </summary>
    private async Task<WarehouseDomain> InsertWarehouseAsync(
        List<CompanyBranch>? companyBranch = null,
        bool isActive = true
    )
    {
        var warehouse = await WarehouseHelpers.InsertWarehouseAsync(
            SharedFixture,
            e2e: false,
            companyBranch: companyBranch,
            isActive: isActive,
            cancellationToken: CancellationToken
        );
        return warehouse;
    }

    /// <summary>
    /// Inserts a supplier order into database.
    /// </summary>
    private async Task<SupplierOrderDomain> InsertSupplierOrderAsync(
        CompanyBranchData? companyBranch = null,
        int state = 3
    )
    {
        var supplierOrder = await SupplierOrderHelpers.InsertSupplierOrderAsync(
            SharedFixture,
            e2e: false,
            userId: null,
            companyBranch: companyBranch,
            stateId: state,
            cancellationToken: CancellationToken
        );
        return supplierOrder;
    }

    #endregion
}
