using BuildingBlocks.Core.Resources;
using BuildingBlocks.DDD.SeedWork.Exceptions;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Validation.Common;
using FluentAssertions;
using MainArchitecture.Application.Models.Common;
using MainArchitecture.Application.Models.Organization;
using MainArchitecture.Domain.Resources;
using MainArchitecture.Domain.SharedKernel.ValueObjects;
using MainArchitecture.TestShared.Fakes.Common.Models;
using MainArchitecture.TestShared.Fakes.Common.ValueObjects;
using MainArchitecture.TestShared.Fakes.Organization.Models;
using Warehouse.API;
using Warehouse.Application.Features.SupplierReceipt.Commands.AddSupplierReceiptDetailFromOrderManually;
using Warehouse.Domain.Aggregates.ProductAggregate.Entities;
using Warehouse.Domain.Aggregates.SupplierReceiptAggregate.Events;
using Warehouse.Domain.Resources;
using Warehouse.Domain.SharedKernel.Enumerations;
using Warehouse.IntegrationTests.Abstractions;
using Warehouse.Persistence.Persistence;
using Warehouse.TestShared.Fakes.Product.Entities;
using Warehouse.TestShared.Fakes.Product.ValueObjects;
using Warehouse.TestShared.Fakes.SupplierReceipt.Requests;
using Warehouse.TestShared.Helpers;
using Xunit.Abstractions;
using ProductDomain = Warehouse.Domain.Aggregates.ProductAggregate.Entities.Product;

namespace Warehouse.IntegrationTests.Features.SupplierReceipt.Commands.AddSupplierReceiptDetailFromOrderManually;

/// <summary>
/// Represents a test suite for adding supplier receipt detail from order manually.
/// </summary>
public class AddSupplierReceiptDetailFromOrderManuallyTests : WarehouseIntegrationTestBase
{
    #region Constructor

    private readonly AddSupplierReceiptDetailFromOrderManuallyCommand _command;

    /// <summary>
    /// Initializes a new instance of the <see cref="AddSupplierReceiptDetailFromOrderManuallyTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public AddSupplierReceiptDetailFromOrderManuallyTests(
        SharedFixtureWithEfCore<Program, WarehouseContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        SharedFixture.SetUserAndOrganization();
        _command = new FakeAddSupplierReceiptDetailFromOrderManuallyCommand().Generate();
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenAllConditionsAreMet_ShouldAddSupplierReceiptDetailFromOrderManually()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();

        var productPrice = new FakeProductPrice().WithBranchId(companyBranchData.BranchId).Generate();
        var product = await InsertProductAsync([companyBranchData], productPrices: [productPrice]);
        var supplierOrder = await SupplierOrderHelpers.InsertSupplierOrderAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            stateId: WarehouseOperationState.Accepted,
            productId: product.Id,
            cancellationToken: CancellationToken
        );
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var productQuantityData = await SharedFixture.ExecuteMappingAsync<ProductQuantity, ProductQuantityData>(
            supplierOrder.Details.Last().ProductQuantity
        );

        var command = new AddSupplierReceiptDetailFromOrderManuallyCommand(
            SupplierReceiptId: supplierReceipt.Id,
            ProductId: supplierOrder.Details.Last().ProductId,
            ProductBatchId: product.ProductBatches.First().Id,
            ProductQuantity: productQuantityData,
            IsGift: false,
            IsOutright: false,
            SupplierOrderId: supplierOrder.Id
        );

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertSuccessResult();
        response.Value.Should().NotBeEmpty();

        supplierReceipt = await SupplierReceiptHelpers.GetSupplierReceiptWithDetailsByDetailIdAndOwnerSpec(
            SharedFixture,
            supplierReceiptDetailId: response,
            ownerId,
            CancellationToken
        );

        supplierReceipt.Should().NotBeNull();
        supplierReceipt!.Details.Should().NotBeEmpty();
        supplierReceipt.Details.Last().Id.Value.Should().Be(response);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierReceiptNotFound_ShouldReturnNotFoundResult()
    {
        // Arrange

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertNotFoundError(DomainResource.Supplier_Receipt);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenProductRequirementNotFound_ShouldReturnNotFoundResult()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();

        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );

        var command = _command with { SupplierReceiptId = supplierReceipt.Id };

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertNotFoundError("Product");
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenProductPriceRequirementNotFound_ShouldReturnNotFoundResult()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();

        var product = await InsertProductAsync([companyBranchData], productPrices: []);
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );

        var command = _command with { SupplierReceiptId = supplierReceipt.Id, ProductId = product.Id };

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertNotFoundError(DomainResource.Product_Price);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenPruductBatchRequirementNotFound_ShouldReturnNotFoundResult()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();

        var productPrice = new FakeProductPrice().WithBranchId(companyBranchData.BranchId).Generate();
        var product = await InsertProductAsync([companyBranchData], productPrices: [productPrice]);
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var command = _command with { SupplierReceiptId = supplierReceipt.Id, ProductId = product.Id };

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertNotFoundError(DomainResource.Product_Batch);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenProductQuantityComputationIsNotValid_ShouldThrowValidationException()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();

        var productPrice = new FakeProductPrice().WithBranchId(companyBranchData.BranchId).Generate();
        var product = await InsertProductAsync([companyBranchData], isActive: false, productPrices: [productPrice]);
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var command = _command with
        {
            SupplierReceiptId = supplierReceipt.Id,
            ProductId = product.Id,
            ProductBatchId = product.ProductBatches.First().Id,
            ProductQuantity = new FakeInvalidProductQuantityData().Generate(),
        };

        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<ValidationException>();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenAlreadyCalledWithSameRequestId_ShouldBeIdempotent()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var supplierOrder = await SupplierOrderHelpers.InsertSupplierOrderAsync(
            SharedFixture,
            e2e: false,
            cancellationToken: CancellationToken
        );

        var command = _command with { SupplierOrderId = supplierOrder.Id };
        await SharedFixture.SendAsync(command, CancellationToken);

        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertIdempotentResult();

        var supplierOrderDetail = await SupplierOrderHelpers.GetSupplierOrdersAsync(SharedFixture, CancellationToken);
        supplierOrderDetail.Count.Should().Be(1);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public Task Send_WhenSupplierReceiptIsNotValid_ShouldThrowValidationException()
    {
        // Arrange
        var command = new FakeInvalidAddSupplierReceiptDetailFromOrderManuallyCommand().Generate();

        // Act & Assert
        return FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<ValidationException>();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierReceiptAccepted_ShouldThrowBusinessRuleException()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();

        var productPrice = new FakeProductPrice().WithBranchId(companyBranchData.BranchId).Generate();
        var product = await InsertProductAsync([companyBranchData], productPrices: [productPrice]);
        var supplierOrder = await SupplierOrderHelpers.InsertSupplierOrderAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            stateId: WarehouseOperationState.Accepted,
            productId: product.Id,
            cancellationToken: CancellationToken
        );
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            companyBranch: companyBranchData,
            stateId: WarehouseOperationState.Accepted,
            cancellationToken: CancellationToken
        );
        var productQuantityData = await SharedFixture.ExecuteMappingAsync<ProductQuantity, ProductQuantityData>(
            supplierOrder.Details.Last().ProductQuantity
        );

        var command = new AddSupplierReceiptDetailFromOrderManuallyCommand(
            SupplierReceiptId: supplierReceipt.Id,
            ProductId: supplierOrder.Details.Last().ProductId,
            ProductBatchId: product.ProductBatches.First().Id,
            ProductQuantity: productQuantityData,
            IsGift: false,
            IsOutright: false,
            SupplierOrderId: supplierOrder.Id
        );

        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<BusinessRuleValidationException>()
            .WithMessage(ProjectResource.Operation_Already_Handled_Error_Message);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierReceiptSent_ShouldThrowBusinessRuleException()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();

        var productPrice = new FakeProductPrice().WithBranchId(companyBranchData.BranchId).Generate();
        var product = await InsertProductAsync([companyBranchData], productPrices: [productPrice]);
        var supplierOrder = await SupplierOrderHelpers.InsertSupplierOrderAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            stateId: WarehouseOperationState.Accepted,
            productId: product.Id,
            cancellationToken: CancellationToken
        );
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            companyBranch: companyBranchData,
            stateId: WarehouseOperationState.Sent,
            cancellationToken: CancellationToken
        );
        var productQuantityData = await SharedFixture.ExecuteMappingAsync<ProductQuantity, ProductQuantityData>(
            supplierOrder.Details.Last().ProductQuantity
        );

        var command = new AddSupplierReceiptDetailFromOrderManuallyCommand(
            SupplierReceiptId: supplierReceipt.Id,
            ProductId: supplierOrder.Details.Last().ProductId,
            ProductBatchId: product.ProductBatches.First().Id,
            ProductQuantity: productQuantityData,
            IsGift: false,
            IsOutright: false,
            SupplierOrderId: supplierOrder.Id
        );
        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<BusinessRuleValidationException>()
            .WithMessage(ProjectResource.Operation_Already_Handled_Error_Message);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierReceiptDetailHasDuplicateProductId_ShouldThrowBusinessRuleException()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();

        var productPrice = new FakeProductPrice().WithBranchId(companyBranchData.BranchId).Generate();
        var product = await InsertProductAsync([companyBranchData], productPrices: [productPrice]);
        var batchInformation = new FakeProductBatchInformation()
            .WithLifeCycleDate(product.ProductBatches.First().LifeCycleDate)
            .WithBatchNumber(product.ProductBatches.First().BatchNumber)
            .Generate();
        var productBatchRequirement = new FakeProductBatchRequirement()
            .WithBatchInformation(batchInformation)
            .Generate();
        var supplierOrder = await SupplierOrderHelpers.InsertSupplierOrderAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            stateId: WarehouseOperationState.Accepted,
            productId: product.Id,
            cancellationToken: CancellationToken
        );
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            productId: product.Id,
            productBatchRequirement: productBatchRequirement,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var productQuantityData = await SharedFixture.ExecuteMappingAsync<ProductQuantity, ProductQuantityData>(
            supplierOrder.Details.Last().ProductQuantity
        );

        var command = new AddSupplierReceiptDetailFromOrderManuallyCommand(
            SupplierReceiptId: supplierReceipt.Id,
            ProductId: supplierOrder.Details.Last().ProductId,
            ProductBatchId: product.ProductBatches.First().Id,
            ProductQuantity: productQuantityData,
            IsGift: false,
            IsOutright: false,
            SupplierOrderId: supplierOrder.Id
        );

        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<BusinessRuleValidationException>()
            .WithMessage(ProjectResource.Duplicate_Error_Message);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierReceiptDetailFromOrderDoNotMatchWithOrdersDetailRule_ShouldThrowBusinessRuleException()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();

        var productPrice = new FakeProductPrice().WithBranchId(companyBranchData.BranchId).Generate();
        var product = await InsertProductAsync([companyBranchData], productPrices: [productPrice]);
        var supplierOrder = await SupplierOrderHelpers.InsertSupplierOrderAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            stateId: WarehouseOperationState.Accepted,
            productId: product.Id,
            cancellationToken: CancellationToken
        );
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var addedSupplierReceiptDetail = supplierReceipt.Details.Last();
        var (singleCount, packetCount, boxCount, totalCount) = addedSupplierReceiptDetail.ProductQuantity;
        var giftQuantity = new FakeProductQuantity()
            .WithSingleCount(singleCount!.Value + 10000f)
            .WithPacketCount(packetCount!.Value + 10000f)
            .WithBoxCount(boxCount!.Value + 10000f)
            .WithTotalCount(totalCount!.Value + 10000f)
            .Generate();
        var QuantityData = await SharedFixture.ExecuteMappingAsync<ProductQuantity, ProductQuantityData>(giftQuantity);

        var command = new AddSupplierReceiptDetailFromOrderManuallyCommand(
            SupplierReceiptId: supplierReceipt.Id,
            ProductId: supplierOrder.Details.Last().ProductId,
            ProductBatchId: product.ProductBatches.First().Id,
            ProductQuantity: QuantityData,
            IsGift: false,
            IsOutright: false,
            SupplierOrderId: supplierOrder.Id
        );

        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<BusinessRuleValidationException>()
            .WithMessage(SharedResource.Invalid_Data_Error_Message);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierReceiptDetailFromOrderDoNotMatchProductWithOrdersDetailRule_ShouldThrowBusinessRuleException()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();

        var productPrice = new FakeProductPrice().WithBranchId(companyBranchData.BranchId).Generate();
        var product = await InsertProductAsync([companyBranchData], productPrices: [productPrice]);
        var supplierOrder = await SupplierOrderHelpers.InsertSupplierOrderAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            stateId: WarehouseOperationState.Accepted,
            productId: product.Id,
            cancellationToken: CancellationToken
        );
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var product2 = await InsertProductAsync([companyBranchData], productPrices: [productPrice]);
        var command = new AddSupplierReceiptDetailFromOrderManuallyCommand(
            SupplierReceiptId: supplierReceipt.Id,
            ProductId: product2.Id,
            ProductBatchId: product.ProductBatches.First().Id,
            ProductQuantity: new FakeProductQuantityData().Generate(),
            IsGift: false,
            IsOutright: false,
            SupplierOrderId: supplierOrder.Id
        );

        // Act & Assert
        await FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<BusinessRuleValidationException>()
            .WithMessage(SharedResource.Invalid_Data_Error_Message);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenSupplierReceiptDetailCreated_ShouldPersistInTheOutbox()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        var companyBranchData =
            await SharedFixture.GetUserCompanyBranchAsync() ?? new FakeCompanyBranchData().Generate();

        var productPrice = new FakeProductPrice().WithBranchId(companyBranchData.BranchId).Generate();
        var product = await InsertProductAsync([companyBranchData], productPrices: [productPrice]);
        var supplierOrder = await SupplierOrderHelpers.InsertSupplierOrderAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            stateId: WarehouseOperationState.Accepted,
            productId: product.Id,
            cancellationToken: CancellationToken
        );
        var supplierReceipt = await SupplierReceiptHelpers.InsertSupplierReceiptAsync(
            SharedFixture,
            e2e: false,
            supplierId: product.SupplierId,
            companyBranch: companyBranchData,
            cancellationToken: CancellationToken
        );
        var productQuantityData = await SharedFixture.ExecuteMappingAsync<ProductQuantity, ProductQuantityData>(
            supplierOrder.Details.Last().ProductQuantity
        );

        var command = new AddSupplierReceiptDetailFromOrderManuallyCommand(
            SupplierReceiptId: supplierReceipt.Id,
            ProductId: supplierOrder.Details.Last().ProductId,
            ProductBatchId: product.ProductBatches.First().Id,
            ProductQuantity: productQuantityData,
            IsGift: false,
            IsOutright: false,
            SupplierOrderId: supplierOrder.Id
        );
        // Act
        var response = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        response.AssertSuccessResult();

        await SharedFixture.ShouldProcessedOutboxPersistMessage<SupplierReceiptDetailAddedDomainEvent>(
            CancellationToken
        );
    }

    #region Privates

    /// <summary>
    /// Inserts a product into the database.
    /// </summary>
    /// <param name="companyBranches">The company branches.</param>
    /// <returns>The inserted product identifier.</returns>
    /// <param name="isActive">The activation status.</param>
    /// <param name="productPrices">The product prices.</param>
    private async Task<ProductDomain> InsertProductAsync(
        List<CompanyBranchData>? companyBranches,
        bool isActive = true,
        List<ProductPrice>? productPrices = null
    )
    {
        var product = await ProductHelpers.InsertProductAsync(
            SharedFixture,
            e2e: false,
            userId: null,
            companyBranch: companyBranches,
            isActive: isActive,
            productPrices: productPrices,
            cancellationToken: CancellationToken
        );
        return product;
    }

    #endregion
}
