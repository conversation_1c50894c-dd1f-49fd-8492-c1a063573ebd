using BuildingBlocks.Core.Resources;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Validation.Common;
using FluentAssertions;
using Warehouse.API;
using Warehouse.Application.Features.Product.Commands.AddProductCollection;
using Warehouse.Domain.Aggregates.ProductAggregate.Events;
using Warehouse.IntegrationTests.Abstractions;
using Warehouse.Persistence.Persistence;
using Warehouse.TestShared.Fakes.Product.Requests;
using Warehouse.TestShared.Helpers;
using Xunit.Abstractions;

namespace Warehouse.IntegrationTests.Features.Product.Commands.AddProductCollection;

public class AddProductCollectionTests : WarehouseIntegrationTestBase
{
    #region Constructor

    private readonly AddProductCollectionCommand _command;

    /// <summary>
    /// Initializes a new instance of the <see cref="AddProductCollectionTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public AddProductCollectionTests(
        SharedFixtureWithEfCore<Program, WarehouseContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        SharedFixture.SetUserAndOrganization();
        _command = new FakeAddProductCollectionCommand().Generate();
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenAllConditionsAreMet_ShouldAddProduct()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var ownerId = await SharedFixture.GetUserIdentityAsync();
        var product = await ProductHelpers.InsertProductAsync(SharedFixture, cancellationToken: CancellationToken);
        var newCommand = _command with { ProductId = product.Id };

        // Act
        var response = await SharedFixture.SendAsync(newCommand, CancellationToken);

        // Assert
        response.AssertSuccessResult();
        response.Value.Should().NotBeEmpty();

        product = await ProductHelpers.GetProductWithCollectionsByCollectionIdAndOwnerAsync(
            SharedFixture,
            response.Value,
            ownerId,
            CancellationToken
        );
        var addedCollection = product!.ProductCollections.Last();
        addedCollection.Should().NotBeNull();
        addedCollection.Id.Value.Should().Be(response.Value);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public Task Send_WhenProductIsNotValid_ShouldThrowValidationException()
    {
        // Arrange
        var command = new FakeInvalidAddProductCollectionCommand().Generate();

        // Act & Assert
        return FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<ValidationException>();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenProductNotFound_ShouldReturnNotFoundResult()
    {
        // Arrange
        // No additional arrangement needed

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertNotFoundError(SharedResource.Product);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenAlreadyCalledWithSameRequestId_ShouldBeIdempotent()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var product = await ProductHelpers.InsertProductAsync(SharedFixture, cancellationToken: CancellationToken);
        var newCommand = _command with { ProductId = product.Id };
        await SharedFixture.SendAsync(newCommand, CancellationToken);

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertIdempotentResult();

        var products = await ProductHelpers.GetProductsAsync(SharedFixture, CancellationToken);
        products.Count.Should().Be(1);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenProductCollectionCreated_ShouldPersistInTheOutbox()
    {
        // Arrange
        SharedFixture.SetUserAndOrganization();
        var product = await ProductHelpers.InsertProductAsync(SharedFixture, cancellationToken: CancellationToken);
        var newCommand = _command with { ProductId = product.Id };
        // Act
        var response = await SharedFixture.SendAsync(newCommand, CancellationToken);

        // Assert
        response.AssertSuccessResult();

        await SharedFixture.ShouldProcessedOutboxPersistMessage<ProductCollectionCreatedDomainEvent>(
            cancellationToken: CancellationToken
        );
    }
}
