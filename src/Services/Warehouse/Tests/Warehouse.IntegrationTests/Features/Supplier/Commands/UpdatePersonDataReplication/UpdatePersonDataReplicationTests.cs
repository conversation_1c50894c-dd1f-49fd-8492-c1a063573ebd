using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Validation.Common;
using FluentAssertions;
using Warehouse.API;
using Warehouse.Application.Features.Supplier.Commands.UpdatePersonDataReplication;
using Warehouse.IntegrationTests.Abstractions;
using Warehouse.Persistence.Persistence;
using Warehouse.TestShared.Fakes.Supplier.Requests;
using Warehouse.TestShared.Helpers;
using Xunit.Abstractions;

namespace Warehouse.IntegrationTests.Features.Supplier.Commands.UpdatePersonDataReplication;

public class UpdatePersonDataReplicationTests : WarehouseIntegrationTestBase
{
    #region Constructor

    private readonly UpdatePersonDataReplicationCommand _command;

    /// <summary>
    /// Initializes a new instance of the <see cref="UpdatePersonDataReplicationTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public UpdatePersonDataReplicationTests(
        SharedFixtureWithEfCore<Program, WarehouseContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        SharedFixture.SetUserAndOrganization();

        _command = new FakeUpdatePersonDataReplicationCommand().Generate();
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenDataExists_ShouldUpdatePersonnelData()
    {
        // Arrange
        var supplierPerson = new FakeSupplierPersonDto().Generate();
        await SupplierHelpers.InsertSupplierAsync(
            SharedFixture,
            e2e: false,
            persons: [supplierPerson],
            cancellationToken: CancellationToken
        );

        var command = _command with { PersonId = supplierPerson.PersonId };

        // Act
        var result = await SharedFixture.SendAsync(command, CancellationToken);

        // Assert
        result.AssertSuccessResult();
        result.Value.Should().BeTrue();

        var updatedSupplier = await SupplierHelpers.GetSupplierPersonsByIdAndOwnerAsync(
            SharedFixture,
            supplierPerson.PersonId,
            CancellationToken
        );
        var updatedSupplierPerson = updatedSupplier!.SupplierPersons.First();

        updatedSupplierPerson.Should().NotBeNull();
        updatedSupplierPerson.PersonId.Value.Should().Be(command.PersonId);
        updatedSupplierPerson.FullName.Should().Be(command.FullName);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenPersonNotFound_ShouldReturnFalse()
    {
        // Arrange

        // Act
        var result = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        result.AssertSuccessResult();
        result.Value.Should().BeFalse();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public Task Send_WhenPersonIsNotValid_ShouldThrowValidationException()
    {
        // Arrange
        var command = new FakeInvalidUpdatePersonDataReplicationCommand().Generate();

        // Act & Assert
        return FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<ValidationException>();
    }
}
