using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using MainArchitecture.Application.Models.FilteringAndPagination;
using Warehouse.API;
using Warehouse.Application.Features.Supplier.Queries.GetFilteredSuppliers;
using Warehouse.IntegrationTests.Abstractions;
using Warehouse.Persistence.Persistence;
using Warehouse.TestShared.Fakes.Supplier.Entities;
using Xunit.Abstractions;
using SupplierDomain = Warehouse.Domain.Aggregates.SupplierAggregate.Entities.Supplier;

namespace Warehouse.IntegrationTests.Features.Supplier.Queries.GetFilteredSuppliers;

/// <summary>
/// Represents the base class for integration tests.
/// </summary>
/// <remarks>
/// This class is used to provide a base class for integration tests.
/// </remarks>
/// <param name="sharedFixture">The shared fixture.</param>
/// <param name="outputHelper">The output helper.</param>
public abstract class GetFilteredSuppliersTestBase(
    SharedFixtureWithEfCore<Program, WarehouseContext> sharedFixture,
    ITestOutputHelper outputHelper
) : WarehouseIntegrationTestBase(sharedFixture, outputHelper)
{
    #region Test Data

    /// <summary>
    /// Gets the supplier input.
    /// </summary>
    protected static readonly SupplierDomain SupplierInput = new FakeSupplier().Generate();

    /// <summary>
    /// Gets represents the pagination test data.
    /// </summary>
    protected const int TotalItems = 5;

    /// <summary>
    /// Gets the base filter.
    /// </summary>
    protected static readonly FilterDto BaseFilter = new();

    /// <summary>
    /// Gets represents the pagination test data.
    /// </summary>
    public static TheoryData<FilterDto, int, int> PaginationTestData =>
        new()
        {
            { BaseFilter, TotalItems, TotalItems },
            { BaseFilter with { Page = 1, PageSize = 2 }, TotalItems, 2 },
            { BaseFilter with { Page = 2, PageSize = 2 }, TotalItems, 2 },
            { BaseFilter with { Page = 3, PageSize = 2 }, TotalItems, 1 },
            { BaseFilter with { Page = 1, PageSize = TotalItems }, TotalItems, TotalItems },
            { BaseFilter with { Page = 2, PageSize = TotalItems }, TotalItems, 0 },
        };

    /// <summary>
    /// Gets represents the search test data.
    /// </summary>
    public static TheoryData<string, bool> SearchTestData =>
        new()
        {
            { SupplierInput.Name, true },
            { SupplierInput.Code, true },
            { SupplierInput.NationalCode, true },
            { SupplierInput.EconomicCode!.Value, true },
            { "NonExistent", false },
        };

    /// <summary>
    /// Gets represents the ordering test data.
    /// </summary>
    public static TheoryData<string, bool> OrderingTestData =>
        new()
        {
            { nameof(GetFilteredSuppliersQueryResponse.Name), true },
            { nameof(GetFilteredSuppliersQueryResponse.Name), false },
            { nameof(GetFilteredSuppliersQueryResponse.Code), true },
            { nameof(GetFilteredSuppliersQueryResponse.Code), false },
            { nameof(GetFilteredSuppliersQueryResponse.NationalCode), true },
            { nameof(GetFilteredSuppliersQueryResponse.NationalCode), false },
            { nameof(GetFilteredSuppliersQueryResponse.EconomicCode), true },
            { nameof(GetFilteredSuppliersQueryResponse.EconomicCode), false },
        };

    /// <summary>
    /// Gets represents the sort key selectors.
    /// </summary>
    public static readonly Dictionary<string, Func<GetFilteredSuppliersQueryResponse, IComparable?>> SortKeySelectors =
        new(StringComparer.Ordinal)
        {
            // Top-level properties
            [nameof(GetFilteredSuppliersQueryResponse.Name)] = x => x.Name,
            [nameof(GetFilteredSuppliersQueryResponse.Code)] = x => x.Code,
            [nameof(GetFilteredSuppliersQueryResponse.NationalCode)] = x => x.NationalCode,
            [nameof(GetFilteredSuppliersQueryResponse.EconomicCode)] = x => x.EconomicCode,
        };

    #endregion

    #region Methods

    /// <summary>
    /// Arranges the data.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    protected async Task ArrangeDataWithSearchTerms(bool exist)
    {
        var owner = await SharedFixture.GetUserIdentityAsync();
        var companyBranchData = await SharedFixture.GetUserCompanyBranchAsync();
        var companyBranch = await SharedFixture.MapToCompanyBranchesAsync(companyBranchData!);
        var supplier = exist
            ? new FakeSupplier()
                .WithOwnerId(owner)
                .WithName(SupplierInput.Name)
                .WithCode(SupplierInput.Code)
                .WithNationalCode(SupplierInput.NationalCode)
                .WithEconomicCode(SupplierInput.EconomicCode!.Value)
                .WithCompanyBranches(companyBranch)
                .Generate()
            : new FakeSupplier().WithOwnerId(owner).Generate();

        await SharedFixture.InsertEfDbContextAsync(supplier, CancellationToken);
    }

    #endregion
}
