using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Validation.Common;
using FluentAssertions;
using MainArchitecture.Domain.SharedKernel.ValueObjects.Organization;
using Warehouse.API;
using Warehouse.Application.Features.OrganizationConfiguration.Commands.UpdateOrganizationConfigurationCompanyDataReplication;
using Warehouse.IntegrationTests.Abstractions;
using Warehouse.Persistence.Persistence;
using Warehouse.TestShared.Fakes.OrganizationConfiguration.Entities;
using Warehouse.TestShared.Fakes.OrganizationConfiguration.Requests;
using Warehouse.TestShared.Helpers;
using Xunit.Abstractions;

namespace Warehouse.IntegrationTests.Features.OrganizationConfiguration.Commands.UpdateOrganizationConfigurationCompanyDataReplication;

/// <summary>
/// Represents the integration tests for the <see cref="UpdateOrganizationConfigurationCompanyDataReplicationCommand"/> class.
/// </summary>
public class UpdateOrganizationConfigurationCompanyDataReplicationTests : WarehouseIntegrationTestBase
{
    #region Constructor

    private readonly UpdateOrganizationConfigurationCompanyDataReplicationCommand _command;

    /// <summary>
    /// Initializes a new instance of the <see cref="UpdateOrganizationConfigurationCompanyDataReplicationTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public UpdateOrganizationConfigurationCompanyDataReplicationTests(
        SharedFixtureWithEfCore<Program, WarehouseContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        SharedFixture.SetUserAndOrganization();
        _command = new FakeUpdateOrganizationConfigurationCompanyDataReplicationCommand().Generate();
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenAllConditionsAreMet_ShouldUpdateCompanyDataReplication()
    {
        // Arrange
        var organizationConfiguration = new FakeOrganizationConfiguration()
            .WithCompanyBranch(
                CompanyBranch.Create(companyId: _command.CompanyId, branchId: SharedFixture.Faker.Random.Guid())
            )
            .Generate();

        await SharedFixture.SendAsync(_command, CancellationToken);
        await SharedFixture.InsertEfDbContextAsync(organizationConfiguration, CancellationToken);

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertSuccessResult();
        response.Value.Should().BeTrue();

        var updatedOrganizationConfiguration =
            await OrganizationConfigurationHelpers.GetOrganizationConfigurationsByCompanyIdAsync(
                sharedFixture: SharedFixture,
                companyId: _command.CompanyId,
                cancellationToken: CancellationToken
            );

        updatedOrganizationConfiguration[0].Should().NotBeNull();
        updatedOrganizationConfiguration[0].CompanyName.Should().Be(_command.CompanyName);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenCompanyNotFound_ShouldReturnFalse()
    {
        // Arrange

        // Act
        var response = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        response.AssertSuccessResult();
        response.Value.Should().BeFalse();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public Task Send_WhenDataIsInvalid_ShouldReturnFalse()
    {
        // Arrange
        var command = new FakeInvalidUpdateOrganizationConfigurationCompanyDataReplicationCommand().Generate();

        // Act & Assert
        return FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<ValidationException>();
    }
}
