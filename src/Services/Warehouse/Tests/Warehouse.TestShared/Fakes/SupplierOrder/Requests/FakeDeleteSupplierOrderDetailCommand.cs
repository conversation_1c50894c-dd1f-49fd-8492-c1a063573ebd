using AutoBogus;
using BuildingBlocks.Core.IdGenerator;
using Warehouse.Application.Features.SupplierOrder.Commands.DeleteSupplierOrderDetail;

namespace Warehouse.TestShared.Fakes.SupplierOrder.Requests;

/// <summary>
/// Represents a fake delete supplier order detail command.
/// </summary>
public sealed class FakeDeleteSupplierOrderDetailCommand : AutoFaker<DeleteSupplierOrderDetailCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeDeleteSupplierOrderDetailCommand"/> class.
    /// </summary>
    public FakeDeleteSupplierOrderDetailCommand()
    {
        RuleFor(r => r.SupplierOrderDetailId, f => f.Random.Guid());
    }
}

/// <summary>
/// Represents a fake invalid delete supplier order detail command.
/// </summary>
public sealed class FakeInvalidDeleteSupplierOrderDetailCommand : AutoFaker<DeleteSupplierOrderDetailCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidDeleteSupplierOrderDetailCommand"/> class.
    /// </summary>
    public FakeInvalidDeleteSupplierOrderDetailCommand()
    {
        RuleFor(r => r.SupplierOrderDetailId, _ => IdGenerator.EmptyId);
    }
}
