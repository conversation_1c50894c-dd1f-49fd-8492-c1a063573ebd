using AutoBogus;
using MainArchitecture.TestShared.Fakes.Common.Models;
using Warehouse.Application.Features.SupplierOrder.Queries.GetSupplierOrderDetailById;

namespace Warehouse.TestShared.Fakes.SupplierOrder.Requests;

/// <summary>
/// Represents a fake get supplier order detail by id query response.
/// </summary>
public sealed class FakeGetSupplierOrderDetailByIdQueryResponse : AutoFaker<GetSupplierOrderDetailByIdQueryResponse>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeGetSupplierOrderDetailByIdQueryResponse"/> class.
    /// </summary>
    public FakeGetSupplierOrderDetailByIdQueryResponse()
    {
        RuleFor(r => r.ProductQuantity, _ => new FakeProductQuantityData().Generate());
    }
}
