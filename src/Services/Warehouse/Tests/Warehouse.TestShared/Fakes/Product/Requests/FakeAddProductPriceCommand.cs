using AutoBogus;
using Warehouse.Application.Features.Product.Commands.AddProductPrice;

namespace Warehouse.TestShared.Fakes.Product.Requests;

/// <summary>
/// Represents a fake add product price command.
/// </summary>
public sealed class FakeAddProductPriceCommand : AutoFaker<AddProductPriceCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeAddProductPriceCommand"/> class.
    /// </summary>
    public FakeAddProductPriceCommand()
    {
        this.ApplySharedRules();
    }
}

/// <summary>
/// Represents a fake invalid add product price command.
/// </summary>
public sealed class FakeInvalidAddProductPriceCommand : AutoFaker<AddProductPriceCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidAddProductPriceCommand"/> class.
    /// </summary>
    public FakeInvalidAddProductPriceCommand()
    {
        this.ApplyInvalidSharedRules();
    }
}
