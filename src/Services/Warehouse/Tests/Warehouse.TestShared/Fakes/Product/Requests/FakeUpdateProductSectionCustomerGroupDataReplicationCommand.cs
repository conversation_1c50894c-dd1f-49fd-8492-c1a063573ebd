using AutoBogus;
using BuildingBlocks.Core.IdGenerator;
using Warehouse.Application.Features.Product.Commands.UpdateProductSectionCustomerGroupDataReplication;

namespace Warehouse.TestShared.Fakes.Product.Requests;

/// <summary>
/// Represents a fake update product section customer group data replication command.
/// </summary>
public sealed class FakeUpdateProductSectionCustomerGroupDataReplicationCommand
    : AutoFaker<UpdateProductSectionCustomerGroupDataReplicationCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeUpdateProductSectionCustomerGroupDataReplicationCommand"/> class.
    /// </summary>
    public FakeUpdateProductSectionCustomerGroupDataReplicationCommand()
    {
        RuleFor(r => r.CustomerGroupId, _ => Guid.NewGuid());
        RuleFor(r => r.CustomerGroupName, f => f.Random.String2(50));
    }
}

/// <summary>
/// Represents a fake invalid update product section customer group data replication command.
/// </summary>
public sealed class FakeInvalidUpdateProductSectionCustomerGroupDataReplicationCommand
    : AutoFaker<UpdateProductSectionCustomerGroupDataReplicationCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidUpdateProductSectionCustomerGroupDataReplicationCommand"/> class.
    /// </summary>
    public FakeInvalidUpdateProductSectionCustomerGroupDataReplicationCommand()
    {
        RuleFor(r => r.CustomerGroupId, _ => IdGenerator.EmptyId);
        RuleFor(r => r.CustomerGroupName, _ => string.Empty);
    }
}
