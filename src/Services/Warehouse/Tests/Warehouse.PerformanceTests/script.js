/*
 * Warehouse.API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * OpenAPI spec version: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://github.com/OpenAPITools/openapi-generator
 *
 * Generator version: 7.10.0
 */


import http from "k6/http";
import { group, check, sleep } from "k6";

const BASE_URL = "/";
// Sleep duration between successive requests.
// You might want to edit the value of this variable or remove calls to the sleep function on the script.
const SLEEP_DURATION = 0.1;
// Global variables should be initialized.
let acceptLanguage = "TODO_EDIT_THE_ACCEPT-LANGUAGE";
let xBranch = "TODO_EDIT_THE_X-BRANCH";
let xCompany = "TODO_EDIT_THE_X-COMPANY";
let xRequestID = "TODO_EDIT_THE_X-REQUEST-ID";

export default function() {
    group("/api/v1/warehouse-service/product-groups", () => {
        let search = 'TODO_EDIT_THE_SEARCH'; // specify value as there is no example value for this parameter in OpenAPI spec
        let isDelete = 'TODO_EDIT_THE_ISDELETE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let pageSize = 'TODO_EDIT_THE_PAGESIZE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderBy = 'TODO_EDIT_THE_ORDERBY'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderByDesc = 'TODO_EDIT_THE_ORDERBYDESC'; // specify value as there is no example value for this parameter in OpenAPI spec
        let page = 'TODO_EDIT_THE_PAGE'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetFilteredProductGroups
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-groups?Search=${Search}&Page=${Page}&PageSize=${PageSize}&OrderBy=${OrderBy}&OrderByDesc=${OrderByDesc}&IsDelete=${IsDelete}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: UpdateProductGroup
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-groups`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "productGroupId": "uuid", "name": "string", "code": "string", "parentId": "uuid"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.put(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 3: AddProductGroup
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-groups`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "name": "string", "code": "string", "parentId": "uuid"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.post(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });
            check(request, {
                "Created": (r) => r.status === 201
            });
        }
    });

    group("/api/v1/warehouse-service/suppliers/{supplierId}/persons", () => {
        let supplierId = 'TODO_EDIT_THE_SUPPLIERID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetSupplierPersonsBySupplierId
        {
            let url = BASE_URL + `/api/v1/warehouse-service/suppliers/${supplierId}/persons`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/product-batches/{productId}/get-all", () => {
        let search = 'TODO_EDIT_THE_SEARCH'; // specify value as there is no example value for this parameter in OpenAPI spec
        let productId = 'TODO_EDIT_THE_PRODUCTID'; // specify value as there is no example value for this parameter in OpenAPI spec
        let pageSize = 'TODO_EDIT_THE_PAGESIZE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderBy = 'TODO_EDIT_THE_ORDERBY'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderByDesc = 'TODO_EDIT_THE_ORDERBYDESC'; // specify value as there is no example value for this parameter in OpenAPI spec
        let page = 'TODO_EDIT_THE_PAGE'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetFilteredProductBatches
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-batches/${productId}/get-all?Search=${Search}&Page=${Page}&PageSize=${PageSize}&OrderBy=${OrderBy}&OrderByDesc=${OrderByDesc}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/warehouse-types/{warehouseTypeId}", () => {
        let warehouseTypeId = 'TODO_EDIT_THE_WAREHOUSETYPEID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetWarehouseTypeById
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouse-types/${warehouseTypeId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: DeleteWarehouseType
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouse-types/${warehouseTypeId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            // this is a DELETE method request - if params are also set, empty body must be passed
            let request = http.del(url, {} , params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/warehouses/mark-as-main", () => {

        // Request No. 1: MarkWarehouseAsMain
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouses/mark-as-main`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "warehouseId": "uuid", "companyBranch": {"companyId": "uuid", "branchId": "uuid"}};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.put(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/warehouse-types", () => {
        let search = 'TODO_EDIT_THE_SEARCH'; // specify value as there is no example value for this parameter in OpenAPI spec
        let isDelete = 'TODO_EDIT_THE_ISDELETE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let pageSize = 'TODO_EDIT_THE_PAGESIZE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderBy = 'TODO_EDIT_THE_ORDERBY'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderByDesc = 'TODO_EDIT_THE_ORDERBYDESC'; // specify value as there is no example value for this parameter in OpenAPI spec
        let page = 'TODO_EDIT_THE_PAGE'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetFilteredWarehouseTypes
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouse-types?Search=${Search}&Page=${Page}&PageSize=${PageSize}&OrderBy=${OrderBy}&OrderByDesc=${OrderByDesc}&IsDelete=${IsDelete}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: UpdateWarehouseType
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouse-types`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "warehouseTypeId": "uuid", "name": "string"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.put(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 3: AddWarehouseType
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouse-types`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "name": "string"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.post(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });
            check(request, {
                "Created": (r) => r.status === 201
            });
        }
    });

    group("/api/v1/warehouse-service/product-batches", () => {

        // Request No. 1: UpdateProductBatch
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-batches`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "productBatchId": "uuid", "batchNumber": "string", "lifeCycleDate": {"startDate": "date", "endDate": "date"}, "batchType": "integer", "irc": "string", "gtin": "string", "uid": "string"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.put(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: AddProductBatch
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-batches`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "productId": "uuid", "batchNumber": "string", "lifeCycleDate": {"startDate": "date", "endDate": "date"}, "batchType": "integer", "irc": "string", "gtin": "string", "uid": "string"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.post(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });
            check(request, {
                "Created": (r) => r.status === 201
            });
        }
    });

    group("/api/v1/warehouse-service/products/{productId}", () => {
        let productId = 'TODO_EDIT_THE_PRODUCTID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetProductById
        {
            let url = BASE_URL + `/api/v1/warehouse-service/products/${productId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: DeleteProduct
        {
            let url = BASE_URL + `/api/v1/warehouse-service/products/${productId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            // this is a DELETE method request - if params are also set, empty body must be passed
            let request = http.del(url, {} , params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/suppliers/change-activation/{supplierId}", () => {
        let supplierId = 'TODO_EDIT_THE_SUPPLIERID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: ChangeSupplierActivation
        {
            let url = BASE_URL + `/api/v1/warehouse-service/suppliers/change-activation/${supplierId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.put(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/brands/{brandId}", () => {
        let brandId = 'TODO_EDIT_THE_BRANDID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetBrandById
        {
            let url = BASE_URL + `/api/v1/warehouse-service/brands/${brandId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: DeleteBrand
        {
            let url = BASE_URL + `/api/v1/warehouse-service/brands/${brandId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            // this is a DELETE method request - if params are also set, empty body must be passed
            let request = http.del(url, {} , params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/warehouses", () => {
        let search = 'TODO_EDIT_THE_SEARCH'; // specify value as there is no example value for this parameter in OpenAPI spec
        let isDelete = 'TODO_EDIT_THE_ISDELETE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let pageSize = 'TODO_EDIT_THE_PAGESIZE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderBy = 'TODO_EDIT_THE_ORDERBY'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderByDesc = 'TODO_EDIT_THE_ORDERBYDESC'; // specify value as there is no example value for this parameter in OpenAPI spec
        let page = 'TODO_EDIT_THE_PAGE'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetFilteredWarehouses
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouses?Search=${Search}&Page=${Page}&PageSize=${PageSize}&OrderBy=${OrderBy}&OrderByDesc=${OrderByDesc}&IsDelete=${IsDelete}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: UpdateWarehouse
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouses`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "warehouseId": "uuid", "warehouseTypeId": "uuid", "name": "string", "assignedCompanyBranches": "list", "unAssignedCompanyBranches": "list"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.put(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 3: AddWarehouse
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouses`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "warehouseTypeId": "uuid", "name": "string", "assignedCompanyBranches": "list"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.post(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });
            check(request, {
                "Created": (r) => r.status === 201
            });
        }
    });

    group("/api/v1/warehouse-service/product-prices/{productPriceId}", () => {
        let productPriceId = 'TODO_EDIT_THE_PRODUCTPRICEID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetProductPriceById
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-prices/${productPriceId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: DeleteProductPrice
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-prices/${productPriceId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            // this is a DELETE method request - if params are also set, empty body must be passed
            let request = http.del(url, {} , params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/products/get-data-for-product-operation", () => {

        // Request No. 1: GetDataForProductOperation
        {
            let url = BASE_URL + `/api/v1/warehouse-service/products/get-data-for-product-operation`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/warehouses/{warehouseId}", () => {
        let warehouseId = 'TODO_EDIT_THE_WAREHOUSEID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetWarehouseById
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouses/${warehouseId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: DeleteWarehouse
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouses/${warehouseId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            // this is a DELETE method request - if params are also set, empty body must be passed
            let request = http.del(url, {} , params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/product-prices/{productId}/get-all", () => {
        let search = 'TODO_EDIT_THE_SEARCH'; // specify value as there is no example value for this parameter in OpenAPI spec
        let productId = 'TODO_EDIT_THE_PRODUCTID'; // specify value as there is no example value for this parameter in OpenAPI spec
        let pageSize = 'TODO_EDIT_THE_PAGESIZE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderBy = 'TODO_EDIT_THE_ORDERBY'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderByDesc = 'TODO_EDIT_THE_ORDERBYDESC'; // specify value as there is no example value for this parameter in OpenAPI spec
        let page = 'TODO_EDIT_THE_PAGE'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetFilteredProductPrices
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-prices/${productId}/get-all?Search=${Search}&Page=${Page}&PageSize=${PageSize}&OrderBy=${OrderBy}&OrderByDesc=${OrderByDesc}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/products", () => {
        let search = 'TODO_EDIT_THE_SEARCH'; // specify value as there is no example value for this parameter in OpenAPI spec
        let isDelete = 'TODO_EDIT_THE_ISDELETE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let pageSize = 'TODO_EDIT_THE_PAGESIZE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderBy = 'TODO_EDIT_THE_ORDERBY'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderByDesc = 'TODO_EDIT_THE_ORDERBYDESC'; // specify value as there is no example value for this parameter in OpenAPI spec
        let page = 'TODO_EDIT_THE_PAGE'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetFilteredProducts
        {
            let url = BASE_URL + `/api/v1/warehouse-service/products?Search=${Search}&Page=${Page}&PageSize=${PageSize}&OrderBy=${OrderBy}&OrderByDesc=${OrderByDesc}&IsDelete=${IsDelete}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: UpdateProduct
        {
            let url = BASE_URL + `/api/v1/warehouse-service/products`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "productId": "uuid", "name": "string", "code": "string", "brandId": "uuid", "supplierId": "uuid", "productPackaging": {"packetNumber": "float", "boxNumber": "float"}, "dimensions": {"length": "float", "width": "float", "height": "float"}, "weight": {"value": "double", "weightUnit": "weightunit"}, "taxDuty": {"taxPercentage": "integer", "dutyPercentage": "integer"}, "salesRatio": "integer", "assignedCompanyBranches": "list", "unAssignedCompanyBranches": "list", "extraProperties": "list", "documents": "list", "assignedProductGroups": "list"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.put(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 3: AddProduct
        {
            let url = BASE_URL + `/api/v1/warehouse-service/products`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "name": "string", "code": "string", "brandId": "uuid", "supplierId": "uuid", "productPackaging": {"packetNumber": "float", "boxNumber": "float"}, "dimensions": {"length": "float", "width": "float", "height": "float"}, "weight": {"value": "double", "weightUnit": "weightunit"}, "taxDuty": {"taxPercentage": "integer", "dutyPercentage": "integer"}, "salesRatio": "integer", "assignedCompanyBranches": "list", "extraProperties": "list", "documents": "list", "assignedProductGroups": "list"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.post(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });
            check(request, {
                "Created": (r) => r.status === 201
            });
        }
    });

    group("/api/v1/warehouse-service/product-prices", () => {

        // Request No. 1: AddProductPrice
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-prices`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "productId": "uuid", "productBatchId": "uuid", "customerId": "uuid", "customerName": "string", "customerGroupId": "uuid", "customerGroupName": "string", "branches": "list", "dateRange": {"startDate": "date", "endDate": "date"}, "buyPrice": {"value": "double", "currency": "integer"}, "sellPrice": {"value": "double", "currency": "integer"}, "consumerPrice": {"value": "double", "currency": "integer"}};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.post(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });
            check(request, {
                "Created": (r) => r.status === 201
            });
        }
    });

    group("/api/v1/warehouse-service/warehouses/change-activation/{warehouseId}", () => {
        let warehouseId = 'TODO_EDIT_THE_WAREHOUSEID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: ChangeWarehouseActivation
        {
            let url = BASE_URL + `/api/v1/warehouse-service/warehouses/change-activation/${warehouseId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.put(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/product-collections", () => {

        // Request No. 1: AddProductCollection
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-collections`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "productId": "uuid", "dateRange": {"startDate": "date", "endDate": "date"}, "period": "integer", "branches": "list", "customerId": "uuid", "customerName": "string", "customerGroupId": "uuid", "customerGroupName": "string"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.post(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });
            check(request, {
                "Created": (r) => r.status === 201
            });
        }
    });

    group("/api/v1/warehouse-service/suppliers", () => {
        let search = 'TODO_EDIT_THE_SEARCH'; // specify value as there is no example value for this parameter in OpenAPI spec
        let isDelete = 'TODO_EDIT_THE_ISDELETE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let pageSize = 'TODO_EDIT_THE_PAGESIZE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderBy = 'TODO_EDIT_THE_ORDERBY'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderByDesc = 'TODO_EDIT_THE_ORDERBYDESC'; // specify value as there is no example value for this parameter in OpenAPI spec
        let page = 'TODO_EDIT_THE_PAGE'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetFilteredSuppliers
        {
            let url = BASE_URL + `/api/v1/warehouse-service/suppliers?Search=${Search}&Page=${Page}&PageSize=${PageSize}&OrderBy=${OrderBy}&OrderByDesc=${OrderByDesc}&IsDelete=${IsDelete}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: UpdateSupplier
        {
            let url = BASE_URL + `/api/v1/warehouse-service/suppliers`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "supplierId": "uuid", "name": "string", "code": "string", "nationalCode": "string", "assignedCompanyBranches": "list", "unAssignedCompanyBranches": "list", "addresses": "list", "economicCode": "string", "telephone": {"countryCode": "integer", "areaCode": "string", "localNumber": "string"}, "mobile": {"countryCode": "integer", "number": "string"}};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.put(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 3: AddSupplier
        {
            let url = BASE_URL + `/api/v1/warehouse-service/suppliers`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "name": "string", "code": "string", "nationalCode": "string", "assignedCompanyBranches": "list", "addresses": "list", "economicCode": "string", "telephone": {"countryCode": "integer", "areaCode": "string", "localNumber": "string"}, "mobile": {"countryCode": "integer", "number": "string"}};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.post(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });
            check(request, {
                "Created": (r) => r.status === 201
            });
        }
    });

    group("/api/v1/warehouse-service/product-groups/{productGroupId}", () => {
        let productGroupId = 'TODO_EDIT_THE_PRODUCTGROUPID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetProductGroupById
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-groups/${productGroupId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: DeleteProductGroup
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-groups/${productGroupId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            // this is a DELETE method request - if params are also set, empty body must be passed
            let request = http.del(url, {} , params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/suppliers/manage-persons-assignment", () => {

        // Request No. 1: ManageSupplierPersonsAssignment
        {
            let url = BASE_URL + `/api/v1/warehouse-service/suppliers/manage-persons-assignment`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "supplierId": "uuid", "excludePersonIds": "list", "newPersons": "list"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.post(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/brands", () => {
        let search = 'TODO_EDIT_THE_SEARCH'; // specify value as there is no example value for this parameter in OpenAPI spec
        let isDelete = 'TODO_EDIT_THE_ISDELETE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let pageSize = 'TODO_EDIT_THE_PAGESIZE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderBy = 'TODO_EDIT_THE_ORDERBY'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderByDesc = 'TODO_EDIT_THE_ORDERBYDESC'; // specify value as there is no example value for this parameter in OpenAPI spec
        let page = 'TODO_EDIT_THE_PAGE'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetFilteredBrands
        {
            let url = BASE_URL + `/api/v1/warehouse-service/brands?Search=${Search}&Page=${Page}&PageSize=${PageSize}&OrderBy=${OrderBy}&OrderByDesc=${OrderByDesc}&IsDelete=${IsDelete}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: UpdateBrand
        {
            let url = BASE_URL + `/api/v1/warehouse-service/brands`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "brandId": "uuid", "name": "string", "assignedCompanyBranches": "list", "unAssignedCompanyBranches": "list"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.put(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 3: AddBrand
        {
            let url = BASE_URL + `/api/v1/warehouse-service/brands`;
            // TODO: edit the parameters of the request body.
            let body = {"id": "uuid", "cacheKeys": "list", "appendRequestHeaders": "boolean", "name": "string", "assignedCompanyBranches": "list"};
            let params = {
                headers: {
                    "Content-Type": "application/json", "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.post(url, JSON.stringify(body), params);

            check(request, {
                "OK": (r) => r.status === 200
            });
            check(request, {
                "Created": (r) => r.status === 201
            });
        }
    });

    group("/api/v1/warehouse-service/suppliers/{supplierId}", () => {
        let supplierId = 'TODO_EDIT_THE_SUPPLIERID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetSupplierById
        {
            let url = BASE_URL + `/api/v1/warehouse-service/suppliers/${supplierId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: DeleteSupplier
        {
            let url = BASE_URL + `/api/v1/warehouse-service/suppliers/${supplierId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            // this is a DELETE method request - if params are also set, empty body must be passed
            let request = http.del(url, {} , params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/product-collections/{productId}/get-all", () => {
        let search = 'TODO_EDIT_THE_SEARCH'; // specify value as there is no example value for this parameter in OpenAPI spec
        let productId = 'TODO_EDIT_THE_PRODUCTID'; // specify value as there is no example value for this parameter in OpenAPI spec
        let pageSize = 'TODO_EDIT_THE_PAGESIZE'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderBy = 'TODO_EDIT_THE_ORDERBY'; // specify value as there is no example value for this parameter in OpenAPI spec
        let orderByDesc = 'TODO_EDIT_THE_ORDERBYDESC'; // specify value as there is no example value for this parameter in OpenAPI spec
        let page = 'TODO_EDIT_THE_PAGE'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetFilteredProductCollections
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-collections/${productId}/get-all?Search=${Search}&Page=${Page}&PageSize=${PageSize}&OrderBy=${OrderBy}&OrderByDesc=${OrderByDesc}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/product-collections/{productCollectionId}", () => {
        let productCollectionId = 'TODO_EDIT_THE_PRODUCTCOLLECTIONID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetProductCollectionById
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-collections/${productCollectionId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: DeleteProductCollection
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-collections/${productCollectionId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            // this is a DELETE method request - if params are also set, empty body must be passed
            let request = http.del(url, {} , params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/products/change-activation/{productId}", () => {
        let productId = 'TODO_EDIT_THE_PRODUCTID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: ChangeProductActivation
        {
            let url = BASE_URL + `/api/v1/warehouse-service/products/change-activation/${productId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.put(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

    group("/api/v1/warehouse-service/product-batches/{productBatchId}", () => {
        let productBatchId = 'TODO_EDIT_THE_PRODUCTBATCHID'; // specify value as there is no example value for this parameter in OpenAPI spec

        // Request No. 1: GetProductBatchById
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-batches/${productBatchId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            let request = http.get(url, params);

            check(request, {
                "OK": (r) => r.status === 200
            });

            sleep(SLEEP_DURATION);
        }

        // Request No. 2: DeleteProductBatch
        {
            let url = BASE_URL + `/api/v1/warehouse-service/product-batches/${productBatchId}`;
            let params = {
                headers: {
                    "X-Company": `${xCompany}`, "X-Branch": `${xBranch}`, "Accept-Language": `${acceptLanguage}`, "X-Request-ID": `${xRequestID}`, "Accept": "application/json"
                }
            };
            // this is a DELETE method request - if params are also set, empty body must be passed
            let request = http.del(url, {} , params);

            check(request, {
                "OK": (r) => r.status === 200
            });
        }
    });

}
