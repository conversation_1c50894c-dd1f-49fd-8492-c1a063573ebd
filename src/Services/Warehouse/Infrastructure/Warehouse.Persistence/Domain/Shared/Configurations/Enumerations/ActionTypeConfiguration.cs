using BuildingBlocks.Core.Types.Extensions;
using BuildingBlocks.Persistence.Contracts;
using BuildingBlocks.Persistence.EntityConfiguration.Enumerations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Warehouse.Domain.SharedKernel.Enumerations;
using Warehouse.Persistence.Persistence;

namespace Warehouse.Persistence.Domain.Shared.Configurations.Enumerations;

/// <summary>
/// Configuration class for the <see cref="ActionType"/> entity, which specifies table schema.
/// primary key, constraints, and other configurations.
/// Implements the IEntityTypeConfiguration interface.
/// </summary>
internal sealed class ActionTypeConfiguration
    : EntityConfigConfigurationContracts<ActionType>,
        IEntityTypeConfiguration<ActionType>
{
    /// <inheritdoc />
    public void Configure(EntityTypeBuilder<ActionType> builder) => builder.Tap(ConfigDataStructure);

    #region Overrides

    /// <inheritdoc />
    public override void ConfigDataStructure(EntityTypeBuilder<ActionType> builder)
    {
        builder.ConfigureEnumeration(
            tableName: nameof(WarehouseContext.ActionTypes),
            schemaName: SchemaNames.Operation
        );
    }

    #endregion
}
