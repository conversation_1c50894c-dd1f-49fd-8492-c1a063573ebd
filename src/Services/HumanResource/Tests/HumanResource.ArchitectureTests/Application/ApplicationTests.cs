using BuildingBlocks.Core.Common.Maybe;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.Tests.ArchitectureTests.Common;
using BuildingBlocks.Tests.ArchitectureTests.CustomRules;
using BuildingBlocks.Tests.ArchitectureTests.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using HumanResource.ArchitectureTests.Abstractions;
using MediatR;
using NetArchTest.Rules;

namespace HumanResource.ArchitectureTests.Application;

/// <summary>
/// Tests for the application layer.
/// </summary>
public class ApplicationTests : BaseTest
{
    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void Commands_Should_BeImmutable()
    {
        Types.InAssembly(ApplicationAssembly).That().PredictCommands().GetTypes().AssertAreImmutable();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void Commands_Should_BeSealed()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictCommands()
            .Should()
            .BeSealed()
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void Commands_Should_HaveCommandPostfix()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictCommands()
            .Should()
            .HaveNameEndingWith(Constants.Postfixes.Command, StringComparison.Ordinal)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void CommandHandlers_Should_BeSealed()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictCommandHandlers()
            .Should()
            .BeSealed()
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void CommandHandlers_Should_HavCommandHandlerPostfix()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictCommandHandlers()
            .Should()
            .HaveNameEndingWith(Constants.Postfixes.CommandHandler, StringComparison.Ordinal)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void CommandHandlers_Should_ReturnResultType()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictCommandHandlers()
            .Should()
            .MeetCustomRule(new ReturnTypeRule(typeof(Result<>)))
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void Queries_Should_BeImmutable()
    {
        Types.InAssembly(ApplicationAssembly).That().PredictQueries().GetTypes().AssertAreImmutable();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void Queries_Should_BeSealed()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictQueries()
            .Should()
            .BeSealed()
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void Queries_Should_HaveQueryPostfix()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictQueries()
            .Should()
            .HaveNameEndingWith(Constants.Postfixes.Query, StringComparison.Ordinal)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void QueryHandlers_Should_BeSealed()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictQueryHandlers()
            .Should()
            .BeSealed()
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void QueryHandlers_Should_ReturnMaybeType()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictQueryHandlers()
            .Should()
            .MeetCustomRule(new ReturnTypeRule(typeof(Maybe<>)))
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void QueryHandlers_Should_HaveQueryHandlerPostfix()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictQueryHandlers()
            .Should()
            .HaveNameEndingWith(Constants.Postfixes.QueryHandler, StringComparison.Ordinal)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void CommandAndQueryHandlers_Should_NotBePublic()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictCommandHandlers()
            .Or()
            .PredictQueryHandlers()
            .Should()
            .NotBePublic()
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void Validators_Should_NotBePublic()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictValidators()
            .Should()
            .NotBePublic()
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void Validators_Should_BeSealed()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictValidators()
            .Should()
            .BeSealed()
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void Validators_Should_HaveValidatorPostfix()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictValidators()
            .Should()
            .HaveNameEndingWith(Constants.Postfixes.Validator, StringComparison.Ordinal)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void DomainEventHandlers_Should_NotBePublic()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictDomainEventHandlers()
            .Should()
            .NotBePublic()
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void DomainEventHandlers_Should_BeSealed()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictDomainEventHandlers()
            .Should()
            .BeSealed()
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void DomainEventHandlers_Should_HaveDomainEventHandlerPostfix()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .PredictDomainEventHandlers()
            .Should()
            .HaveNameEndingWith(Constants.Postfixes.DomainEventHandler, StringComparison.Ordinal)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void MediatorRequestHandlers_Should_NotBeUsedDirectly()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .DoNotHaveName($"I{Constants.Postfixes.CommandHandler}`1")
            .Or()
            .DoNotHaveName($"I{Constants.Postfixes.QueryHandler}`1")
            .Should()
            .ImplementInterface(typeof(IRequestHandler<,>))
            .GetTypes()
            .AssertOnlyHaveRequestHandlers(typeof(IRequestHandler<,>));
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void MediatorNotificationHandlers_Should_NotBeUsedDirectly()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .That()
            .DoNotHaveName($"I{Constants.Postfixes.DomainEventHandler}`1")
            .Should()
            .ImplementInterface(typeof(INotificationHandler<>))
            .GetTypes()
            .AssertOnlyHaveRequestHandlers(typeof(INotificationHandler<>));
    }
}
