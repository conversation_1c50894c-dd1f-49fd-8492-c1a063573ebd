using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Validation.Common;
using FluentAssertions;
using HumanResource.API;
using HumanResource.Application.Features.Personnel.Commands.ChangePersonDeletion;
using HumanResource.IntegrationTests.Abstractions;
using HumanResource.Persistence.Persistence;
using HumanResource.TestShared.Fakes.Personnel.Entities;
using HumanResource.TestShared.Fakes.Personnel.Requests;
using HumanResource.TestShared.Helpers;
using Xunit.Abstractions;
using PersonnelDomain = HumanResource.Domain.Aggregates.PersonnelAggregate.Entities.Personnel;

namespace HumanResource.IntegrationTests.Features.Personnel.Commands.ChangePersonDeletion;

/// <summary>
/// Represents the tests for the <see cref="ChangePersonDeletionCommand"/> query.
/// </summary>
public class ChangePersonDeletionTests : HumanResourceIntegrationTestBase
{
    #region Constructor

    private readonly ChangePersonDeletionCommand _command;
    private readonly PersonnelDomain _personnel;

    /// <summary>
    /// Initializes a new instance of the <see cref="ChangePersonDeletionTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public ChangePersonDeletionTests(
        SharedFixtureWithEfCore<Program, HumanResourceContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        SharedFixture.SetUserAndOrganization();

        var personId = SharedFixture.Faker.Random.Guid();
        _personnel = new FakePersonnel().WithPersonId(personId).Generate();
        _command = new FakeChangePersonDeletionCommand().Generate() with { PersonId = personId };
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenDataExists_ShouldUpdatePersonData()
    {
        // Arrange
        await SharedFixture.InsertEfDbContextAsync(_personnel, CancellationToken);

        // Act
        var result = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        result.AssertSuccessResult();
        result.Value.Should().BeTrue();

        var updatedPersonnel = await PersonnelHelpers.GetPersonnelByIdAndOwnerAsync(
            SharedFixture,
            _personnel.Id,
            _personnel.OwnerId,
            CancellationToken
        );
        updatedPersonnel.Should().NotBeNull();
        updatedPersonnel!.Id.Should().Be(_personnel.Id);
        updatedPersonnel.PersonId.Value.Should().Be(_command.PersonId);
        updatedPersonnel.IsPersonDeleted.Should().Be(_command.IsDeleted);
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenPersonNotFound_ShouldDoNothing()
    {
        // Arrange

        // Act
        var result = await SharedFixture.SendAsync(_command, CancellationToken);

        // Assert
        result.AssertSuccessResult();
        result.Value.Should().BeFalse();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public Task Send_WhenPersonnelIsNotValid_ShouldThrowValidationException()
    {
        // Arrange
        var command = new FakeInvalidChangePersonDeletionCommand().Generate();

        // Act & Assert
        return FluentActions
            .Invoking(() => SharedFixture.SendAsync(command, CancellationToken))
            .Should()
            .ThrowAsync<ValidationException>();
    }
}
