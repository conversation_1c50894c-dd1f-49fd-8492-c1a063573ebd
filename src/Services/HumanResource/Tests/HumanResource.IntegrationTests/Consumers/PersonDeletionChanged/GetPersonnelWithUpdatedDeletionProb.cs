using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.IntegrationTests.Helpers;
using HumanResource.API;
using HumanResource.Domain.Aggregates.PersonnelAggregate.Entities;
using HumanResource.Domain.Aggregates.PersonnelAggregate.ValueObjects;
using HumanResource.Domain.Resources;
using HumanResource.Persistence.Persistence;
using HumanResource.TestShared.Helpers;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds.Organization;

namespace HumanResource.IntegrationTests.Consumers.PersonDeletionChanged;

/// <summary>
/// Represents a probe for getting personnel with updated deletion status.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="GetPersonnelWithUpdatedDeletionProb"/> class.
/// </remarks>
/// <param name="sharedFixture">The shared fixture.</param>
/// <param name="personnelId">The personnel identifier.</param>
/// <param name="personId">The person identifier.</param>
/// <param name="isDeleted">The deletion status.</param>
/// <param name="cancellationToken">The cancellation token.</param>
internal sealed class GetPersonnelWithUpdatedDeletionProb(
    SharedFixtureWithEfCore<Program, HumanResourceContext> sharedFixture,
    PersonnelId personnelId,
    PersonId personId,
    bool isDeleted,
    CancellationToken cancellationToken
) : IProbe
{
    /// <summary>
    /// The personnels.
    /// </summary>
    private List<Personnel> _personnels = [];

    /// <inheritdoc />
    public bool IsSatisfied() => _personnels.Exists(p => p.Id == personnelId && p.IsPersonDeleted == isDeleted);

    /// <inheritdoc />
    public async Task SampleAsync()
    {
        _personnels = await PersonnelHelpers.GetPersonnelsByPersonIdAsync(sharedFixture, personId, cancellationToken);
    }

    /// <inheritdoc />
    public string DescribeFailureTo() => SharedErrorMessages.NotFoundErrorMessage(DomainResource.Personnel);
}
