using Ardalis.Specification;
using BuildingBlocks.DDD.SeedWork.Specification;
using HumanResource.Domain.Aggregates.PersonnelAggregate.Entities;
using HumanResource.Domain.Aggregates.PersonnelAggregate.ValueObjects;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds;

namespace HumanResource.Domain.Aggregates.PersonnelAggregate.Specifications;

/// <summary>
/// Represents a specification used to select a <see cref="Personnel"/> by its identifier and owner.
/// </summary>
public sealed class PersonnelByIdAndOwnerSpec
    : SpecificationBase<Personnel, PersonnelId>,
        ISingleResultSpecification<Personnel>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="PersonnelByIdAndOwnerSpec"/> class.
    /// </summary>
    /// <param name="personnelId">The personnel identifier.</param>
    /// <param name="ownerId">The owner identifier.</param>
    /// <param name="isReadOnly">Indicates whether the result is read-only.</param>
    public PersonnelByIdAndOwnerSpec(PersonnelId personnelId, UserId ownerId, bool isReadOnly)
    {
        Query.ByIdForOwner(personnelId, ownerId).AsNoTracking(isReadOnly);
    }
}
