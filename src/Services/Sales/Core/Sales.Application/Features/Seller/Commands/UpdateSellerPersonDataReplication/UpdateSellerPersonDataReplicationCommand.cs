using BuildingBlocks.CQRS.Commands;
using MainArchitecture.Application.Models.Organization;

namespace Sales.Application.Features.Seller.Commands.UpdateSellerPersonDataReplication;

/// <summary>
/// This command is handled by <see cref="UpdateSellerPersonDataReplicationCommandHandler"/> class.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="UpdateSellerPersonDataReplicationCommand"/> class.
/// </remarks>
/// <param name="PersonId">The ID of the person associated with the seller.</param>
/// <param name="FullName">The full name of the person associated with the seller.</param>
public sealed record UpdateSellerPersonDataReplicationCommand(Guid PersonId, string FullName)
    : InternalCommandBase<bool>,
        IPersonDataReplicationDto;
