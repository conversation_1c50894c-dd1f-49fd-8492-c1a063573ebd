using BuildingBlocks.CQRS.Commands;

namespace Sales.Application.Features.Seller.Commands.ChangeSellerActivation;

/// <summary>
/// This command is handled by <see cref="ChangeSellerActivationCommandHandler"/> class.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="ChangeSellerActivationCommand"/> class.
/// </remarks>
/// <param name="SellerId">The ID of the seller record to activate.</param>
public sealed record ChangeSellerActivationCommand(Guid SellerId) : IdempotentCommand<bool>;
