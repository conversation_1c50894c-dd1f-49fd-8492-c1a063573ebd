namespace Sales.Application.Features.Customer.Models.Common;

/// <summary>
/// Represents the customer collection data.
/// </summary>
/// <param name="TypeId">The type identifier.</param>
/// <param name="Period">The period.</param>
public sealed record CustomerCollectionData(int TypeId, int Period)
{
    /// <summary>
    /// Initializes a new instance of the <see cref="CustomerCollectionData"/> class.
    /// </summary>
    /// <remarks>
    /// for dapper mapping usage.
    /// </remarks>
    public CustomerCollectionData()
        : this(0, 0) { }
}
