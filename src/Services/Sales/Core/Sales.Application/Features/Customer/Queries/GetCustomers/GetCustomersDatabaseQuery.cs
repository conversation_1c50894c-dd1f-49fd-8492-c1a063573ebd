using BuildingBlocks.Core.Common.Maybe;
using BuildingBlocks.CQRS.Queries;
using BuildingBlocks.Dapper.Connection;
using Dapper;
using MainArchitecture.Application.Models.Common;

namespace Sales.Application.Features.Customer.Queries.GetCustomers;

/// <summary>
/// Represents a database query for retrieving customers.
/// </summary>
public interface IGetCustomersDatabaseQuery : IDatabaseQuery
{
    /// <summary>
    /// Handles the query.
    /// </summary>
    /// <param name="ownerId">The owner identifier.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The query result.</returns>
    Task<Maybe<IReadOnlyCollection<OptionVm>>> Handle(Guid ownerId, CancellationToken cancellationToken);
}

/// <summary>
/// Represents a database query for retrieving customers.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="GetCustomersDatabaseQuery"/> class.
/// </remarks>
/// <param name="connectionFactory">The database connection factory.</param>
internal sealed class GetCustomersDatabaseQuery(IDbConnectionFactory connectionFactory) : IGetCustomersDatabaseQuery
{
    /// <inheritdoc />
    public async Task<Maybe<IReadOnlyCollection<OptionVm>>> Handle(Guid ownerId, CancellationToken cancellationToken)
    {
        await using var connection = await connectionFactory.GetOrCreateConnectionAsync(cancellationToken);
        var sql = BuildBaseQuery();

        var result = await connection.QueryAsync<OptionVm>(sql, GetParameters(ownerId));
        return result.AsList();
    }

    #region Privates

    /// <summary>
    /// Constructs the base SQL query for retrieving customers.
    /// </summary>
    /// <returns>The base SQL query as a string.</returns>
    private static string BuildBaseQuery()
    {
        const string sql = $"""
            SELECT
               [c].[Id] AS {nameof(OptionVm.Id)},
               [c].[Name] AS {nameof(OptionVm.Text)}
            FROM
               [customer].[Customers] AS c WITH(NOLOCK)
            WHERE
               [c].[OwnerId] = @OwnerId
               AND [c].[IsDelete] = 0
            """;

        return sql;
    }

    /// <summary>
    /// Get parameters for the query.
    /// </summary>
    /// <returns>The parameters.</returns>
    private object GetParameters(Guid ownerId)
    {
        return new { OwnerId = ownerId };
    }

    #endregion
}
