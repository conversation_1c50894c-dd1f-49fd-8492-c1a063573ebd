using BuildingBlocks.CQRS.Commands;
using Sales.Application.Features.SalesGroup.Models.Dto;

namespace Sales.Application.Features.SalesGroup.Commands.UpdateSalesGroup;

/// <summary>
/// This command is handled by <see cref="UpdateSalesGroupCommandHandler"/> class.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="UpdateSalesGroupCommand"/> class.
/// </remarks>
/// <param name="SalesGroupId">The sales group ID.</param>
/// <param name="Name">The name of the sales group.</param>
public sealed record UpdateSalesGroupCommand(Guid SalesGroupId, string Name) : IdempotentCommand<bool>, ISalesGroupDto;
