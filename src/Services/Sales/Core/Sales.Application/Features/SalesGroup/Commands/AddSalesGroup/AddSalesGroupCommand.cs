using BuildingBlocks.CQRS.Commands;
using Sales.Application.Features.SalesGroup.Models.Dto;

namespace Sales.Application.Features.SalesGroup.Commands.AddSalesGroup;

/// <summary>
/// This command is handled by <see cref="AddSalesGroupCommandHandler"/> class.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="AddSalesGroupCommand"/> class.
/// </remarks>
/// <param name="Name">The name of the sales group.</param>
public sealed record AddSalesGroupCommand(string Name) : IdempotentCommand<Guid>, ISalesGroupDto;
