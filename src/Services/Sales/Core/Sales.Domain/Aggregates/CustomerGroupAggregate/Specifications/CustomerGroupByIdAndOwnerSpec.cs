using Ardalis.Specification;
using BuildingBlocks.DDD.SeedWork.Specification;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds.Sales;
using Sales.Domain.Aggregates.CustomerGroupAggregate.Entities;

namespace Sales.Domain.Aggregates.CustomerGroupAggregate.Specifications;

/// <summary>
/// Represents a specification used to select a <see cref="CustomerGroup"/> by its identifier and owner.
/// </summary>
public sealed class CustomerGroupByIdAndOwnerSpec
    : SpecificationBase<CustomerGroup, CustomerGroupId>,
        ISingleResultSpecification<CustomerGroup>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="CustomerGroupByIdAndOwnerSpec"/> class.
    /// </summary>
    /// <param name="customerGroupId">The customer group identifier.</param>
    /// <param name="ownerId">The owner identifier.</param>
    /// <param name="isReadOnly">Indicates whether the result is read-only.</param>
    public CustomerGroupByIdAndOwnerSpec(CustomerGroupId customerGroupId, UserId ownerId, bool isReadOnly)
    {
        Query.ByIdForOwner(customerGroupId, ownerId).Include(pg => pg.Children).AsNoTracking(isReadOnly);
    }
}
