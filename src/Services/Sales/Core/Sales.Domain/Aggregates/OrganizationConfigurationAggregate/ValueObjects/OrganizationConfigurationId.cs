using BuildingBlocks.DDD.SeedWork.Primitives;

namespace Sales.Domain.Aggregates.OrganizationConfigurationAggregate.ValueObjects;

/// <summary>
/// Represents a company branch config identifier.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="OrganizationConfigurationId"/> class.
/// </remarks>
/// <param name="Value">The value of the company branch config identifier.</param>
public sealed record OrganizationConfigurationId(Guid Value) : TypedIdValueBase(Value)
{
    /// <summary>
    /// Initializes a new instance of the <see cref="OrganizationConfigurationId"/> class.
    /// </summary>
    /// <param name="value">The value.</param>
    /// <returns>The <see cref="OrganizationConfigurationId"/> company branch config identifier.</returns>
    public static OrganizationConfigurationId Create(Guid value) => new(value);

    /// <summary>
    /// Implicitly converts the company branch config identifier to a guid.
    /// </summary>
    /// <param name="organizationConfigurationId">The company branch config identifier.</param>
    public static implicit operator Guid(OrganizationConfigurationId organizationConfigurationId) =>
        organizationConfigurationId.Value;

    /// <summary>
    /// Implicitly converts the guid to a company branch config identifier.
    /// </summary>
    /// <param name="companyBranchConfigId">The company branch config identifier.</param>
    public static implicit operator OrganizationConfigurationId(Guid companyBranchConfigId) =>
        Create(companyBranchConfigId);
}
