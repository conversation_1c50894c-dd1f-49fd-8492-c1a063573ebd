using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using MainArchitecture.Domain.Resources;
using MainArchitecture.TestShared.Fakes.Organization.Models;
using NSubstitute;
using Sales.Application.Features.Route.Commands.DeleteRoute;
using Sales.Domain.Aggregates.RouteAggregate.Data;
using Sales.TestShared.Fakes.Route.Entities;
using Sales.TestShared.Fakes.Route.Requests;
using Sales.UnitTests.Abstractions;

namespace Sales.UnitTests.Route.Features.Commands.DeleteRoute;

/// <summary>
/// Represents the unit tests for the <see cref="DeleteRouteCommandHandler"/> class.
/// </summary>
public class DeleteRouteCommandHandlerTests : SalesUnitTestBase
{
    #region Constructor

    private readonly IRouteRepository _routeRepository;
    private readonly DeleteRouteCommandHandler _handler;

    /// <summary>
    /// Initializes a new instance of the <see cref="DeleteRouteCommandHandlerTests"/> class.
    /// </summary>
    /// <param name="fixture">The fixture.</param>
    public DeleteRouteCommandHandlerTests(SalesUnitTestFixture fixture)
        : base(fixture)
    {
        Fixture.SetupOrganizationAccessor(organizationOwnership: new FakeOrganizationOwnershipDto().Generate());
        _routeRepository = Substitute.For<IRouteRepository>();
        _handler = new DeleteRouteCommandHandler(_routeRepository, Fixture.OrganizationAccessor);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenDataIsValid_ShouldReturnTrue()
    {
        // Arrange
        var command = new FakeDeleteRouteCommand().Generate();
        var route = new FakeRoute().Generate();
        ArrangeFirstOrDefault(_routeRepository, route);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.AssertSuccessResult();
        await AssertRepositoryDeleteAsync(_routeRepository, route);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenRouteNotFound_ShouldReturnFalse()
    {
        // Arrange
        var command = new FakeDeleteRouteCommand().Generate();
        ArrangeFirstOrDefault(_routeRepository);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.AssertNotFoundError(ProjectResource.Route);
        await AssertRepositoryUpdateCancelledAsync(_routeRepository);
    }
}
