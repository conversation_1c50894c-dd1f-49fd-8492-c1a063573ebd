using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using Sales.Application.Features.Route.Mappings;
using Sales.Application.Features.Route.Models.Dto;
using Sales.Domain.Aggregates.RouteAggregate.ValueObjects;
using Sales.TestShared.Fakes.Route.Requests;
using Sales.TestShared.Fakes.Route.ValueObjects;
using Sales.UnitTests.Abstractions;

namespace Sales.UnitTests.Route.Features.Mappings;

/// <summary>
/// Represents the unit tests for the <see cref="RouteMappingProfile"/> class.
/// </summary>
public class RouteMappingProfileTests : SalesUnitTestBase
{
    /// <summary>
    /// Initializes a new instance of the <see cref="RouteMappingProfileTests"/> class.
    /// </summary>
    /// <param name="fixture">The test fixture.</param>
    public RouteMappingProfileTests(SalesUnitTestFixture fixture)
        : base(fixture) { }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void RouteVisitationScheduleData_ShouldMapToRouteVisitationSchedule()
    {
        // Arrange
        var routeVisitationScheduleData = new FakeRouteVisitationScheduleData().Generate();

        // Act
        var result = Fixture.Mapper.Map<RouteVisitationSchedule>(routeVisitationScheduleData);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(routeVisitationScheduleData.Count);
        result.Duration.Should().Be(routeVisitationScheduleData.Duration);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void RouteVisitationSchedule_ShouldMapToRouteVisitationScheduleData()
    {
        // Arrange
        var routeVisitationSchedule = new FakeRouteVisitationSchedule().Generate();

        // Act
        var result = Fixture.Mapper.Map<RouteVisitationScheduleData>(routeVisitationSchedule);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(routeVisitationSchedule.Count);
        result.Duration.Should().Be(routeVisitationSchedule.Duration);
    }
}
