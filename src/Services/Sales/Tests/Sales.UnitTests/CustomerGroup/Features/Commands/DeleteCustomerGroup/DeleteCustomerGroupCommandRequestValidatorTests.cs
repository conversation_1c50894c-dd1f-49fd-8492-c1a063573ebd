using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using FluentValidation.TestHelper;
using Microsoft.Extensions.Logging.Abstractions;
using Sales.Application.Features.CustomerGroup.Commands.DeleteCustomerGroup;
using Sales.TestShared.Fakes.CustomerGroup.Requests;

namespace Sales.UnitTests.CustomerGroup.Features.Commands.DeleteCustomerGroup;

/// <summary>
/// Represents the tests for the <see cref="DeleteCustomerGroupCommandRequestValidator"/> class.
/// </summary>
public class DeleteCustomerGroupCommandRequestValidatorTests
{
    #region Constructor

    private readonly DeleteCustomerGroupCommandRequestValidator _validator;

    /// <summary>
    /// Initializes a new instance of the <see cref="DeleteCustomerGroupCommandRequestValidatorTests"/> class.
    /// </summary>
    public DeleteCustomerGroupCommandRequestValidatorTests()
    {
        _validator = new DeleteCustomerGroupCommandRequestValidator(
            NullLogger<DeleteCustomerGroupCommandRequestValidator>.Instance
        );
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenValidInput_ShouldReturnTrue()
    {
        // Arrange
        var commandInput = new FakeDeleteCustomerGroupCommandRequest().Generate();

        // Act
        var result = _validator.TestValidate(commandInput);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenInvalidInput_ShouldReturnFalse()
    {
        // Arrange
        var commandInput = new FakeInvalidDeleteCustomerGroupCommandRequest().Generate();

        // Act
        var result = _validator.TestValidate(commandInput);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.CustomerGroupId);
    }
}
