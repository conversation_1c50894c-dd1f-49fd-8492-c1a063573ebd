using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using MainArchitecture.Application.Configuration.Errors;
using MainArchitecture.Application.Models.Organization;
using MainArchitecture.TestShared.Fakes.Organization.Models;
using NSubstitute;
using Sales.Application.Features.Customer.Queries.GetCustomerPersonsByCustomerId;
using Sales.TestShared.Fakes.Customer.Requests;
using Sales.UnitTests.Abstractions;

namespace Sales.UnitTests.Customer.Features.Queries.GetCustomerPersonsByCustomerId;

/// <summary>
/// Represents the unit test for the <see cref="GetCustomerPersonsByCustomerIdQueryHandler"/> class.
/// </summary>
public class GetCustomerPersonsByCustomerIdQueryHandlerTests : SalesUnitTestBase
{
    #region Constructor

    private readonly GetCustomerPersonsByCustomerIdQueryHandler _handler;
    private readonly IGetCustomerPersonsByCustomerIdDatabaseQuery _databaseQuery;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetCustomerPersonsByCustomerIdQueryHandlerTests"/> class.
    /// </summary>
    public GetCustomerPersonsByCustomerIdQueryHandlerTests(SalesUnitTestFixture fixture)
        : base(fixture)
    {
        _databaseQuery = Substitute.For<IGetCustomerPersonsByCustomerIdDatabaseQuery>();
        _handler = new GetCustomerPersonsByCustomerIdQueryHandler(_databaseQuery, Fixture.OrganizationAccessor);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task HandleAsync_WhenDatabaseQueryReturnsSomeResult_ReturnsSomeResult()
    {
        // Arrange
        Fixture.SetupOrganizationAccessor(
            companyBranch: new FakeCompanyBranchData().Generate(),
            organizationOwnership: new FakeOrganizationOwnershipDto().Generate()
        );
        var query = new FakeGetCustomerPersonsByCustomerIdQuery().Generate();
        var expectedResponse = new FakeGetCustomerPersonsByCustomerIdQueryResponse().Generate(10);

        _databaseQuery
            .Handle(query, Arg.Any<Guid>(), Arg.Any<CompanyBranchData>(), Arg.Any<CancellationToken>())
            .Returns(expectedResponse);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.AssertFounded(expectedResponse);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenOrganizationNotSpecified_ShouldReturnError()
    {
        // Arrange
        Fixture.SetupOrganizationAccessor();
        var query = new FakeGetCustomerPersonsByCustomerIdQuery().Generate();
        var expectedResponse = new FakeGetCustomerPersonsByCustomerIdQueryResponse().Generate(10);

        _databaseQuery
            .Handle(query, Arg.Any<Guid>(), Arg.Any<CompanyBranchData>(), Arg.Any<CancellationToken>())
            .Returns(expectedResponse);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.AssertProblemMaybe();
        result.Error.Should().Be(ProjectErrorMessages.OrganizationNotSpecified());
    }
}
