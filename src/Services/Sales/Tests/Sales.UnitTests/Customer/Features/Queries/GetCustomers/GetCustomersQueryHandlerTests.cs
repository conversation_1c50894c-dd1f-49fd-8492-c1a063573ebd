using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using MainArchitecture.TestShared.Fakes.Common.Models;
using NSubstitute;
using Sales.Application.Features.Customer.Queries.GetCustomers;
using Sales.TestShared.Fakes.Customer.Requests;
using Sales.UnitTests.Abstractions;

namespace Sales.UnitTests.Customer.Features.Queries.GetCustomers;

/// <summary>
/// Represents the unit test for the <see cref="GetCustomersQueryHandler"/> class.
/// </summary>
public class GetCustomersQueryHandlerTests : SalesUnitTestBase
{
    #region Constructor

    private readonly GetCustomersQueryHandler _handler;
    private readonly IGetCustomersDatabaseQuery _databaseQuery;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetCustomersQueryHandlerTests"/> class.
    /// </summary>
    public GetCustomersQueryHandlerTests(SalesUnitTestFixture fixture)
        : base(fixture)
    {
        _databaseQuery = Substitute.For<IGetCustomersDatabaseQuery>();
        _handler = new GetCustomersQueryHandler(_databaseQuery, Fixture.OrganizationAccessor);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task HandleAsync_WhenDatabaseQueryReturnsSomeResult_ReturnsSomeResult()
    {
        // Arrange
        var query = new FakeGetCustomersQuery().Generate();
        var expectedResponse = new FakeOptionVm().Generate(2);

        _databaseQuery.Handle(Arg.Any<Guid>(), Arg.Any<CancellationToken>()).Returns(expectedResponse);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.AssertFounded(expectedResponse);
    }
}
