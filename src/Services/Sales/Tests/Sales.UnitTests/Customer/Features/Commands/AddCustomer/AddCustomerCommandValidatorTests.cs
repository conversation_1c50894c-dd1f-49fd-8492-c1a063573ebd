using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Tests.UnitTests.Abstractions;
using FluentAssertions;
using FluentValidation.TestHelper;
using Microsoft.Extensions.Logging.Abstractions;
using Sales.Application.Features.Customer.Commands.AddCustomer;
using Sales.TestShared.Fakes.Customer.Requests;
using Sales.UnitTests.Customer.Features.Commands.Helpers;

namespace Sales.UnitTests.Customer.Features.Commands.AddCustomer;

/// <summary>
/// Represents the unit tests for the <see cref="AddCustomerCommandValidator"/> class.
/// </summary>
public class AddCustomerCommandValidatorTests : BaseApplicationUnitTest
{
    #region Constructor

    private readonly AddCustomerCommandValidator _validator;

    /// <summary>
    /// Initializes a new instance of the <see cref="AddCustomerCommandValidatorTests"/> class.
    /// </summary>
    public AddCustomerCommandValidatorTests()
    {
        _validator = new AddCustomerCommandValidator(NullLogger<AddCustomerCommandValidator>.Instance);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenValidInput_ShouldReturnTrue()
    {
        // Arrange
        var command = new FakeAddCustomerCommand().Generate();

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenInvalidInput_ShouldReturnFalse()
    {
        // Arrange
        var command = new FakeInvalidAddCustomerCommand().Generate();

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.AssertInvalidCustomer(command);
    }
}
