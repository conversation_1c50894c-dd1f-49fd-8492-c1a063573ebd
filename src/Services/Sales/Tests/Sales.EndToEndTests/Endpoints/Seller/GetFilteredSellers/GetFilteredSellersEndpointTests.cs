using BuildingBlocks.Tests.IntegrationTests.Auth;
using BuildingBlocks.Tests.IntegrationTests.Extensions;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.XunitCategories;
using BuildingBlocks.Web.Problems;
using FluentAssertions;
using MainArchitecture.Application.Models.FilteringAndPagination;
using MainArchitecture.Application.Models.Organization;
using MainArchitecture.Domain.SharedKernel.Enumerations;
using MainArchitecture.Infrastructure.Extensions;
using MainArchitecture.TestShared.Fakes.Common.Models;
using Sales.API.Endpoints.Seller;
using Sales.Application.Features.Seller.Queries.GetFilteredSellers;
using Sales.EndToEndTests.Abstractions;
using Sales.Persistence.Persistence;
using Sales.TestShared.Helpers;
using Xunit.Abstractions;

namespace Sales.EndToEndTests.Endpoints.Seller.GetFilteredSellers;

/// <summary>
/// Represents the tests for the <see cref="SellerEndpoints"/> class.
/// </summary>
public class GetFilteredSellersEndpointTests : SalesEndToEndTestBase
{
    #region Constructor

    private const int TotalItems = 2;
    private readonly bool _isOwner;
    private readonly CompanyBranchData _companyBranch;
    private readonly string _mainRoute;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetFilteredSellersEndpointTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public GetFilteredSellersEndpointTests(
        SharedFixtureWithEfCore<API.Program, SalesContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        _isOwner = SharedFixture.Faker.Random.Bool();
        _companyBranch = sharedFixture.GetOrganizationFromRequest(_isOwner);
        var filter = new FakeFilterDto().Generate();
        _mainRoute = filter.UrlWithQueryParams(Constants.Routes.Seller.GetFilteredSellers);
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenRequestIsValid_ShouldReturnOkResult()
    {
        // Arrange
        ArrangeSuccessAllAuthFilters(_isOwner);
        await SellerHelpers.InsertSellersAsync(
            SharedFixture,
            e2e: true,
            companyBranchData: _companyBranch,
            totalItems: TotalItems,
            cancellationToken: CancellationToken
        );

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: _isOwner)
            .GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response
            .Should()
            .BeOk<PaginatedResult<GetFilteredSellersQueryResponse>>(res =>
            {
                res.Data.Should().NotBeEmpty();
                res.TotalItems.Should().Be(TotalItems);
            });

        await SharedFixture.AssertCacheAllAuthFilters(
            SystemPart.Seller,
            AccessLevel.Get,
            _isOwner,
            cancellationToken: CancellationToken
        );
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenRequestIsNotValid_ShouldReturnValidationBadRequestResult()
    {
        // Arrange
        ArrangeSuccessAllAuthFilters(_isOwner);

        var filter = new FakeInvalidFilterDto().Generate();

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: _isOwner)
            .GetAsync(
                filter.UrlWithQueryParams(Constants.Routes.Seller.GetFilteredSellers),
                cancellationToken: CancellationToken
            );

        // Assert
        response.Should().NotBeValid();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenUserIsNotAuthorized_ShouldReturnUnauthorizedResult()
    {
        // Arrange
        var httpClient = SharedFixture.GuestClient;

        // Act
        var response = await httpClient.GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response.Should().Be401Unauthorized();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenOrganizationNotSpecified_ShouldReturnForbiddenResult()
    {
        // Arrange
        var httpClient = SharedFixture.NormalUserHttpClient.WithRequestId();

        // Act
        var response = await httpClient.GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotHaveOrganization();
    }

    [Theory]
    [MemberData(nameof(InvalidNotOwnedOrganizationData))]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenOrganizationInfoIsNotValid_ShouldReturnForbiddenResult(OrganizationValidationData data)
    {
        // Arrange
        var httpClient = SharedFixture.NormalUserHttpClient.AttachHttpClientHeaders(isOwner: data.IsOwner);
        SetOrganizationOwnershipAccess(hasValue: data.HasValue, isOwner: data.IsOwner, owner: data.OwnerId);

        // Act
        var response = await httpClient.GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response.Should().HasProblemDetail(new ForbiddenProblemDetails(detail: data.Error)).And.Be403Forbidden();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenDoesNotHaveMembership_ShouldReturnForbiddenResult()
    {
        // Arrange
        ArrangeFailedMembershipAuthFilters(_isOwner);

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: _isOwner)
            .GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotHaveMembership();
        await SharedFixture.AssertCacheInvalidMembership(_isOwner, CancellationToken);
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task Get_WhenUserDoesNotHavePermission_ShouldReturnForbiddenResult()
    {
        // Arrange
        ArrangedFailedPermissionAuthFilters();

        await SellerHelpers.InsertSellersAsync(
            SharedFixture,
            e2e: true,
            totalItems: TotalItems,
            cancellationToken: CancellationToken
        );

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.AttachHttpClientHeaders(isOwner: false)
            .GetAsync(_mainRoute, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotHavePermission();
        await SharedFixture.AssertCacheInvalidPermission(SystemPart.Seller, AccessLevel.Get, CancellationToken);
    }
}
