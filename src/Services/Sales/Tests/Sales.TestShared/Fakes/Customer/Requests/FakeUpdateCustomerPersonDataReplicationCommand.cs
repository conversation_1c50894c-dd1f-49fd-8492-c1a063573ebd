using AutoBogus;
using BuildingBlocks.Core.IdGenerator;
using Sales.Application.Features.Customer.Commands.UpdateCustomerPersonDataReplication;

namespace Sales.TestShared.Fakes.Customer.Requests;

/// <summary>
/// Represents a fake update customer person data replication command.
/// </summary>
public sealed class FakeUpdateCustomerPersonDataReplicationCommand
    : AutoFaker<UpdateCustomerPersonDataReplicationCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeUpdateCustomerPersonDataReplicationCommand"/> class.
    /// </summary>
    public FakeUpdateCustomerPersonDataReplicationCommand()
    {
        RuleFor(x => x.PersonId, f => f.Random.Guid());
        RuleFor(x => x.FullName, f => f.Name.FullName());
    }
}

/// <summary>
/// Represents a fake invalid update customer person data replication command.
/// </summary>
public sealed class FakeInvalidUpdateCustomerPersonDataReplicationCommand
    : AutoFaker<UpdateCustomerPersonDataReplicationCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidUpdateCustomerPersonDataReplicationCommand"/> class.
    /// </summary>
    public FakeInvalidUpdateCustomerPersonDataReplicationCommand()
    {
        RuleFor(x => x.PersonId, _ => IdGenerator.EmptyId);
        RuleFor(x => x.FullName, _ => string.Empty);
    }
}
