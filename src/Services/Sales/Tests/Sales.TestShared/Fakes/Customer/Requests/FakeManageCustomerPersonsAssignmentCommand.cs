using AutoBogus;
using BuildingBlocks.Core.IdGenerator;
using Sales.Application.Features.Customer.Commands.ManageCustomerPersonsAssignment;

namespace Sales.TestShared.Fakes.Customer.Requests;

/// <summary>
/// Represents a fake manage customer persons assignment command.
/// </summary>
public sealed class FakeManageCustomerPersonsAssignmentCommand : AutoFaker<ManageCustomerPersonsAssignmentCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeManageCustomerPersonsAssignmentCommand"/> class.
    /// </summary>
    public FakeManageCustomerPersonsAssignmentCommand()
    {
        RuleFor(r => r.CustomerId, f => f.Random.Guid());
        RuleFor(r => r.ExcludePersonIds, f => new List<Guid>(f.Random.Int(min: 1, max: 10)));
        RuleFor(r => r.NewPersons, _ => new FakeCustomerPersonDto().Generate(2));
    }
}

/// <summary>
/// Represents a fake invalid manage customer persons assignment command.
/// </summary>
public sealed class FakeInvalidManageCustomerPersonsAssignmentCommand
    : AutoFaker<ManageCustomerPersonsAssignmentCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidManageCustomerPersonsAssignmentCommand"/> class.
    /// </summary>
    public FakeInvalidManageCustomerPersonsAssignmentCommand()
    {
        RuleFor(r => r.CustomerId, _ => IdGenerator.EmptyId);
        RuleFor(r => r.NewPersons, _ => new FakeInvalidCustomerPersonDto().Generate(2));
    }
}
