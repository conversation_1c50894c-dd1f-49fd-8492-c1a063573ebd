using AutoBogus;
using BuildingBlocks.Core.IdGenerator;
using Sales.Application.Features.Customer.Commands.RestoreCustomer;

namespace Sales.TestShared.Fakes.Customer.Requests;

/// <summary>
/// Represents a fake restore customer command request.
/// </summary>
public sealed class FakeRestoreCustomerCommandRequest : AutoFaker<RestoreCustomerCommandRequest>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeRestoreCustomerCommandRequest"/> class.
    /// </summary>
    public FakeRestoreCustomerCommandRequest()
    {
        RuleFor(r => r.CustomerId, f => f.Random.Guid());
    }
}

/// <summary>
/// Represents a fake invalid restore customer command request.
/// </summary>
public sealed class FakeInvalidRestoreCustomerCommandRequest : AutoFaker<RestoreCustomerCommandRequest>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidRestoreCustomerCommandRequest"/> class.
    /// </summary>
    public FakeInvalidRestoreCustomerCommandRequest()
    {
        RuleFor(r => r.CustomerId, _ => IdGenerator.EmptyId);
    }
}
