using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.IntegrationTests.Helpers;
using MainArchitecture.Domain.Resources;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds.Organization;
using Sales.API;
using Sales.Persistence.Persistence;
using Sales.TestShared.Helpers;
using CustomerDomain = Sales.Domain.Aggregates.CustomerAggregate.Entities.Customer;

namespace Sales.IntegrationTests.Consumers.CustomerPersonDeletionChanged;

/// <summary>
/// Represents a probe for getting customer with updated deletion status.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="GetCustomerWithDeletedPersonProb"/> class.
/// </remarks>
/// <param name="sharedFixture">The shared fixture.</param>
/// <param name="personId">The person identifier.</param>
/// <param name="isDeleted">The deletion status.</param>
/// <param name="cancellationToken">The cancellation token.</param>
internal sealed class GetCustomerWithDeletedPersonProb(
    SharedFixtureWithEfCore<Program, SalesContext> sharedFixture,
    PersonId personId,
    bool isDeleted,
    CancellationToken cancellationToken
) : IProbe
{
    /// <summary>
    /// The customers.
    /// </summary>
    private readonly List<CustomerDomain> _customers = [];

    /// <inheritdoc />
    public bool IsSatisfied() =>
        _customers.Exists(p => p.CustomerPersons.Any(c => c.PersonId == personId && c.IsPersonDeleted == isDeleted));

    /// <inheritdoc />
    public async Task SampleAsync()
    {
        var customer = (
            await CustomerHelpers.GetCustomerPersonsByIdAndOwnerAsync(sharedFixture, personId, cancellationToken)
        )!;
        _customers.Add(customer);
    }

    /// <inheritdoc />
    public string DescribeFailureTo() => SharedErrorMessages.NotFoundErrorMessage(ProjectResource.Customer);
}
