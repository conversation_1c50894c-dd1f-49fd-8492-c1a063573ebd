using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using Sales.API;
using Sales.Application.Features.Customer.Queries.GetCustomers;
using Sales.IntegrationTests.Abstractions;
using Sales.Persistence.Persistence;
using Sales.TestShared.Helpers;
using Xunit.Abstractions;

namespace Sales.IntegrationTests.Features.Customer.Queries.GetCustomers;

/// <summary>
/// Represents the tests for the <see cref="GetCustomersQuery"/> query.
/// </summary>
public class GetCustomersTests : SalesIntegrationTestBase
{
    /// <summary>
    /// Initializes a new instance of the <see cref="GetCustomersTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public GetCustomersTests(
        SharedFixtureWithEfCore<Program, SalesContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        SharedFixture.SetUserAndOrganization();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenDataExists_ShouldReturnData()
    {
        // Arrange
        await CustomerHelpers.InsertCustomersAsync(
            SharedFixture,
            e2e: false,
            totalItems: 2,
            cancellationToken: CancellationToken
        );
        var query = new GetCustomersQuery();

        // Act
        var result = await SharedFixture.QueryAsync(query, CancellationToken);

        // Assert
        result.AssertSuccessMaybe();
        result.Value.Should().NotBeEmpty();
    }

    [Fact]
    [CategoryTrait(TestCategory.Integration)]
    public async Task Send_WhenDataDoesNotExists_ShouldReturnEmpty()
    {
        // Arrange
        var query = new GetCustomersQuery();

        // Act
        var result = await SharedFixture.QueryAsync(query, CancellationToken);

        // Assert
        result.AssertSuccessMaybe();
        result.Value.Should().BeEmpty();
    }
}
