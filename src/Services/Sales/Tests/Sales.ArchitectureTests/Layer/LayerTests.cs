using BuildingBlocks.Tests.ArchitectureTests.Extensions;
using BuildingBlocks.Tests.Shared.XunitCategories;
using NetArchTest.Rules;
using Sales.ArchitectureTests.Abstractions;

namespace Sales.ArchitectureTests.Layer;

/// <summary>
/// Represents the layer tests.
/// </summary>
public class LayerTests : BaseTest
{
    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void DomainLayer_ShouldNotHaveDependencyOn_ApplicationLayer()
    {
        Types
            .InAssembly(DomainAssembly)
            .Should()
            .NotHaveDependencyOn(ApplicationAssembly.GetName().Name)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void DomainLayer_ShouldNotHaveDependencyOn_InfrastructureLayer()
    {
        Types
            .InAssembly(DomainAssembly)
            .Should()
            .NotHaveDependencyOn(InfrastructureAssembly.GetName().Name)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void DomainLayer_ShouldNotHaveDependencyOn_PersistenceLayer()
    {
        Types
            .InAssembly(DomainAssembly)
            .Should()
            .NotHaveDependencyOn(PersistenceAssembly.GetName().Name)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void DomainLayer_ShouldNotHaveDependencyOn_PresentationLayer()
    {
        Types
            .InAssembly(DomainAssembly)
            .Should()
            .NotHaveDependencyOn(PresentationAssembly.GetName().Name)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void ApplicationLayer_ShouldNotHaveDependencyOn_InfrastructureLayer()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .Should()
            .NotHaveDependencyOn(InfrastructureAssembly.GetName().Name)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void ApplicationLayer_ShouldNotHaveDependencyOn_PersistenceLayer()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .Should()
            .NotHaveDependencyOn(PersistenceAssembly.GetName().Name)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void ApplicationLayer_ShouldNotHaveDependencyOn_PresentationLayer()
    {
        Types
            .InAssembly(ApplicationAssembly)
            .Should()
            .NotHaveDependencyOn(PresentationAssembly.GetName().Name)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void PersistenceLayer_ShouldNotHaveDependencyOn_PresentationLayer()
    {
        Types
            .InAssembly(PersistenceAssembly)
            .Should()
            .NotHaveDependencyOn(PresentationAssembly.GetName().Name)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void InfrastructureLayer_ShouldNotHaveDependencyOn_PersistenceLayer()
    {
        Types
            .InAssembly(InfrastructureAssembly)
            .Should()
            .NotHaveDependencyOn(PresentationAssembly.GetName().Name)
            .GetResult()
            .ShouldBeSuccessful();
    }

    [Fact]
    [CategoryTrait(TestCategory.Architecture)]
    public void InfrastructureLayer_ShouldNotHaveDependencyOn_PresentationLayer()
    {
        Types
            .InAssembly(InfrastructureAssembly)
            .Should()
            .NotHaveDependencyOn(PresentationAssembly.GetName().Name)
            .GetResult()
            .ShouldBeSuccessful();
    }
}
