using BuildingBlocks.Core.Constants;
using BuildingBlocks.Core.Types.Extensions;
using BuildingBlocks.Persistence.Contracts;
using BuildingBlocks.Persistence.EntityConfiguration.Entities;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds.Sales;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Sales.Domain.Aggregates.CustomerAggregate.Entities;
using Sales.Persistence.Persistence;

namespace Sales.Persistence.Domain.Customer.Configurations.Entities;

/// <summary>
/// Configuration class for the <see cref="CustomerPerson"/> entity to specify the table schema, primary key, default values, and other constraints.
/// Implements the IEntityTypeConfiguration interface.
/// </summary>
internal sealed class CustomerPersonConfiguration
    : EntityConfigConfigurationContracts<CustomerPerson>,
        IEntityTypeConfiguration<CustomerPerson>
{
    /// <inheritdoc />
    public void Configure(EntityTypeBuilder<CustomerPerson> builder) =>
        builder.Tap(ConfigDataStructure).Tap(ConfigIndexes);

    #region Overrides

    /// <inheritdoc />
    public override void ConfigDataStructure(EntityTypeBuilder<CustomerPerson> builder)
    {
        builder.ConfigureEntity(tableName: nameof(SalesContext.CustomerPersons), schemaName: SchemaNames.Customer);

        builder.HasKey(nameof(CustomerId), nameof(CustomerPerson.PersonId));

        builder.Property<CustomerId>(nameof(CustomerId)).HasColumnName(nameof(CustomerId)).IsRequired();

        builder.Property(b => b.PersonId).IsRequired();

        // Configure the person data replication.
        builder
            .Property(b => b.FullName)
            .HasMaxLength(
                GlobalDomainConstValues.ContactFirstNameMaxLength + GlobalDomainConstValues.ContactLastNameMaxLength
            )
            .IsRequired();

        builder.Property(b => b.IsPersonDeleted).IsRequired();
    }

    #endregion
}
