using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sales.Persistence.Migrations;

/// <inheritdoc />
public partial class ChangeCustomerCreditRelation : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_CustomerCredits_CustomerCollectionTypes_CustomerCollectionTypeId",
            schema: "customer",
            table: "CustomerCredits"
        );

        migrationBuilder.DropIndex(
            name: "IX_CustomerCredits_CustomerCollectionTypeId",
            schema: "customer",
            table: "CustomerCredits"
        );

        migrationBuilder.DropColumn(name: "CustomerCollectionTypeId", schema: "customer", table: "CustomerCredits");

        migrationBuilder.CreateIndex(
            name: "IX_CustomerCredits_Collection_CustomerCollectionTypeId",
            schema: "customer",
            table: "CustomerCredits",
            column: "Collection_CustomerCollectionTypeId"
        );

        migrationBuilder.AddForeignKey(
            name: "FK_CustomerCredits_CustomerCollectionTypes_Collection_CustomerCollectionTypeId",
            schema: "customer",
            table: "CustomerCredits",
            column: "Collection_CustomerCollectionTypeId",
            principalSchema: "customer",
            principalTable: "CustomerCollectionTypes",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade
        );
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_CustomerCredits_CustomerCollectionTypes_Collection_CustomerCollectionTypeId",
            schema: "customer",
            table: "CustomerCredits"
        );

        migrationBuilder.DropIndex(
            name: "IX_CustomerCredits_Collection_CustomerCollectionTypeId",
            schema: "customer",
            table: "CustomerCredits"
        );

        migrationBuilder.AddColumn<int>(
            name: "CustomerCollectionTypeId",
            schema: "customer",
            table: "CustomerCredits",
            type: "int",
            nullable: false,
            defaultValue: 0
        );

        migrationBuilder.CreateIndex(
            name: "IX_CustomerCredits_CustomerCollectionTypeId",
            schema: "customer",
            table: "CustomerCredits",
            column: "CustomerCollectionTypeId"
        );

        migrationBuilder.AddForeignKey(
            name: "FK_CustomerCredits_CustomerCollectionTypes_CustomerCollectionTypeId",
            schema: "customer",
            table: "CustomerCredits",
            column: "CustomerCollectionTypeId",
            principalSchema: "customer",
            principalTable: "CustomerCollectionTypes",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade
        );
    }
}
