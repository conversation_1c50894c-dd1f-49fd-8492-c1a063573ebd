using System.Reflection;

namespace Organization.API;

/// <summary>
/// Represents a reference to the "Organization.API" project.
/// </summary>
public static class AssemblyReference
{
    /// <summary>
    /// Gets the project name as "Organization.API".
    /// </summary>
    public static string ProjectName => "Organization.API";

    /// <summary>
    /// Gets the type of the "Program" class, which is the entry point of the "Organization.API" project.
    /// </summary>
    public static Type Type => typeof(Program);

    /// <summary>
    /// Gets the assembly of the "AssemblyReference" class, which is the assembly of the "Organization.API" project.
    /// </summary>
    public static Assembly Assembly => Type.Assembly;
}
