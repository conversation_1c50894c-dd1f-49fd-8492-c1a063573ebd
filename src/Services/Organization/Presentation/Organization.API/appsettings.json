{"Services": {"auth": {"http": "http://localhost:5003"}, "account-grpc": {"http": "http://localhost:5005"}, "organization-grpc": {"http": "http://localhost:5009"}}, "Kestrel": {"Endpoints": {"Grpc": {"Protocols": "Http1", "Url": "http://localhost:5008"}, "WebApi": {"Protocols": "Http2", "Url": "http://localhost:5009"}}}, "ConnectionStrings": {"Connection": "Server=localhost;Database=OrganizationDb;User Id=sa;Password=*********;TrustServerCertificate=True;"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Warning"}}}, "OpenApiSettings": {"ApplicationName": "Organization.API", "CompanyName": "UltraDataGroup", "CompanyWebSite": "https://ultradatagroup.com", "LicenceType": "MIT", "CompanyEmail": "<EMAIL>", "Provider": 2}, "Jwt": {"Issuer": "http://auth", "Aud": "organization.api", "SecretKey": "HJ87Mb69XFe5qA2dJJ6TsBUSUNGpyqd7KnJQ/FxeRro="}, "EventBusSettings": {"HostAddress": "amqp://guest:guest@localhost:5672", "ExchangeName": "organization.api", "ExponentialRetryCount": 5, "ExponentialRetryInterval": 1, "ExponentialRetryIntervalDelta": 30, "ExponentialRetryIntervalMax": 5, "OutboxQueryDelay": 1, "OutBoxDuplicateDetectionWindow": 30}, "ElasticConfiguration": {"Uri": "http://localhost:9200"}, "SeqConfiguration": {"Uri": "http://localhost:5341"}, "Caching": {"Enabled": true, "QueryCacheTimeInMinutes": 30, "AuthConfigCacheTimeInMinutes": 1440, "Host": "localhost", "Port": 6379}, "PolicySettings": {"RetryCount": 5, "SleepDuration": 2, "TimeoutInSeconds": 30}, "OutboxSettings": {"IntervalInSeconds": 2, "BatchSize": 100}, "OpenTelemetry": {"ActivitySourceName": "ActivitySource.Organization.Api", "ServiceName": "Organization.Api", "ServiceVersion": "1.0.0", "ExporterEndpoint": "http://localhost:4317"}, "LuckyPennyOptions": {"MediatRLicenseKey": "eyJhbGciOiJSUzI1NiIsImtpZCI6Ikx1Y2t5UGVubnlTb2Z0d2FyZUxpY2Vuc2VLZXkvYmJiMTNhY2I1OTkwNGQ4OWI0Y2IxYzg1ZjA4OGNjZjkiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************.O-Gv74POhPymKHfxJeG-Y9vjE8P-RVhOrzFRbXsqMnxf_zzDMZPQ4od7k8fovQeQkX45jx4gsBt8MYMXC6V4cPKgWiy1NRQzsQdjscQA1nToV0qCEXPg-bSidcAWcccOTxE1xrVXPsed3lULpL8Rakj0wcJdrovS7JSd9s8oAUabWXyyf-p03HPsegfO1Y1V5mdbTlvUMfeaU0qAfSakAPItfVVRORz54Rd5F3KjJY7J_zGZEugFNNByAZNFo9QIVs2qlU6rI4RgdCik8itDY_3H1r1T9bJCLQqgn6gDr-kqRqF86GToPSqmB-dOkKJkND08Zi9S8Z0xGd2IsT2reg", "AutoMapperLicenseKey": "eyJhbGciOiJSUzI1NiIsImtpZCI6Ikx1Y2t5UGVubnlTb2Z0d2FyZUxpY2Vuc2VLZXkvYmJiMTNhY2I1OTkwNGQ4OWI0Y2IxYzg1ZjA4OGNjZjkiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************.O-Gv74POhPymKHfxJeG-Y9vjE8P-RVhOrzFRbXsqMnxf_zzDMZPQ4od7k8fovQeQkX45jx4gsBt8MYMXC6V4cPKgWiy1NRQzsQdjscQA1nToV0qCEXPg-bSidcAWcccOTxE1xrVXPsed3lULpL8Rakj0wcJdrovS7JSd9s8oAUabWXyyf-p03HPsegfO1Y1V5mdbTlvUMfeaU0qAfSakAPItfVVRORz54Rd5F3KjJY7J_zGZEugFNNByAZNFo9QIVs2qlU6rI4RgdCik8itDY_3H1r1T9bJCLQqgn6gDr-kqRqF86GToPSqmB-dOkKJkND08Zi9S8Z0xGd2IsT2reg"}, "AllowedHosts": "*"}