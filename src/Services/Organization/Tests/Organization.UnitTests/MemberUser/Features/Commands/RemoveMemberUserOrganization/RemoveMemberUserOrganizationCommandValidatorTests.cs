using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using FluentValidation.TestHelper;
using Microsoft.Extensions.Logging.Abstractions;
using Organization.Application.Features.MemberUser.Commands.RemoveMemberUserOrganization;
using Organization.TestShared.Fakes.MemberUser.Requests;

namespace Organization.UnitTests.MemberUser.Features.Commands.RemoveMemberUserOrganization;

/// <summary>
/// Represents the unit tests for the <see cref="RemoveMemberUserOrganizationCommandValidator"/> class.
/// </summary>
public class RemoveMemberUserOrganizationCommandValidatorTests
{
    #region Constructor

    private readonly RemoveMemberUserOrganizationCommandValidator _validator;

    /// <summary>
    /// Initializes a new instance of the <see cref="RemoveMemberUserOrganizationCommandValidatorTests"/> class.
    /// </summary>
    public RemoveMemberUserOrganizationCommandValidatorTests()
    {
        _validator = new RemoveMemberUserOrganizationCommandValidator(
            NullLogger<RemoveMemberUserOrganizationCommandValidator>.Instance
        );
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenValidInput_ShouldReturnTrue()
    {
        // Arrange
        var commandInput = new FakeRemoveMemberUserOrganizationCommand().Generate();

        // Act
        var result = _validator.TestValidate(commandInput);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void Validate_WhenInvalidInput_ShouldReturnFalse()
    {
        // Arrange
        var commandInput = new FakeInvalidRemoveMemberUserOrganizationCommand().Generate();

        // Act
        var result = _validator.TestValidate(commandInput);

        // Assert
        result.IsValid.Should().BeFalse();
        result.ShouldHaveValidationErrorFor(x => x.UserId);
        result.ShouldHaveValidationErrorFor(x => x.Organization);
    }
}
