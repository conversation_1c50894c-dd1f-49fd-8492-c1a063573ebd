using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using MainArchitecture.TestShared.Fakes.Common.Models;
using Organization.API.Grpc.GeographicalArea.Mappings;
using Organization.Application.Features.GeographicalArea.Queries.GetGeographicalAreasByTypeAndLink;
using Organization.GrpcServices;
using Organization.TestShared.Fakes.GeographicalArea.Requests;
using Organization.UnitTests.Abstractions;

namespace Organization.UnitTests.GeographicalArea.Grpc.Mappings;

/// <summary>
/// Represents the unit tests for the <see cref="GeographicalAreaMappingProfile"/> class.
/// </summary>
public class GeographicalAreaMappingProfileGrpcTests(OrganizationUnitTestFixture fixture)
    : OrganizationUnitTestBase(fixture)
{
    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void GeographicalAreaGrpcRequest_ShouldMapToGeographicalAreaQuery()
    {
        // Arrange
        var grpcRequest = new FakeGetGeographicalAreasGrpcRequest().Generate();

        // Act
        var result = Fixture.Mapper.Map<GetGeographicalAreasByTypeAndLinkQuery>(grpcRequest);

        // Assert
        result.Should().NotBeNull();
        result.TypeId.Should().Be(grpcRequest.TypeId);
        result.LinkId.Should().Be(grpcRequest.LinkId);
    }

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public void GeographicalAreaGrpcResponse_ShouldMapToGeographicalAreaGrpcResponse()
    {
        // Arrange
        var queryResponses = new FakeOptionVm().Generate(3);

        // Act
        var result = Fixture.Mapper.Map<GetGeographicalAreasGrpcResponseList>(queryResponses);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().NotBeNull();
        result.Data.Should().HaveCount(3);
        result.Data.Should().Contain(d => queryResponses.Exists(r => r.Id == d.Id && r.Text == d.Text));
    }
}
