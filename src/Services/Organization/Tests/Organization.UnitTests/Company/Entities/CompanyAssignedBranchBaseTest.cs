using BuildingBlocks.Tests.UnitTests.Abstractions;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds.Organization;
using Organization.Domain.Aggregates.CompanyAggregate.Entities;
using Organization.TestShared.Fakes.Company.Entities;

namespace Organization.UnitTests.Company.Entities;

/// <summary>
/// Represents the base test for the company assigned branch domain.
/// </summary>
public abstract class CompanyAssignedBranchBaseTest : BaseDomainUnitTest
{
    #region Fields

    /// <summary>
    /// The branch ID.
    /// </summary>
    protected readonly BranchId BranchId = BranchId.Create(Faker.Random.Guid());

    /// <summary>
    /// The company assigned branch.
    /// </summary>
    protected readonly CompanyAssignedBranch CompanyAssignedBranch = new FakeCompanyAssignedBranch().Generate();

    #endregion
}
