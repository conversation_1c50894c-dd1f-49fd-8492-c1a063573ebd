using BuildingBlocks.Tests.Shared.XunitCategories;
using MainArchitecture.IntegrationEvents.Events.Organization;
using Microsoft.Extensions.Logging.Abstractions;
using Organization.Application.Features.Company.Events;
using Organization.TestShared.Fakes.Company.Events;
using Organization.UnitTests.Abstractions;

namespace Organization.UnitTests.Company.Features.Events;

/// <summary>
/// Represents the tests for the <see cref="CompanyUpdatedDomainEventHandler"/> class.
/// </summary>
public class CompanyUpdatedDomainEventHandlerTests : OrganizationUnitTestBase
{
    #region Constructor

    private readonly CompanyUpdatedDomainEventHandler _handler;

    /// <summary>
    /// Initializes a new instance of the <see cref="CompanyUpdatedDomainEventHandlerTests"/> class.
    /// </summary>
    /// <param name="fixture">The fixture.</param>
    public CompanyUpdatedDomainEventHandlerTests(OrganizationUnitTestFixture fixture)
        : base(fixture)
    {
        _handler = new CompanyUpdatedDomainEventHandler(
            Fixture.IntegrationEventService,
            Fixture.Mapper,
            NullLogger<CompanyUpdatedDomainEventHandler>.Instance
        );
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.Unit)]
    public async Task Handle_WhenCalled_ShouldPublishCompanyUpdatedIntegrationEvent()
    {
        // Arrange
        var domainEvent = new FakeCompanyUpdatedDomainEvent().Generate();

        // Act
        await _handler.Handle(domainEvent, CancellationToken.None);

        // Assert
        await AssertIntegrationEventPublishedAsync<CompanyUpdatedIntegrationEvent>(Fixture.IntegrationEventService);
    }
}
