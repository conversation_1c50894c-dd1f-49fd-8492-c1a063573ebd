using BuildingBlocks.Tests.IntegrationTests.Extensions;
using BuildingBlocks.Tests.IntegrationTests.Fixtures;
using BuildingBlocks.Tests.Shared.XunitCategories;
using FluentAssertions;
using MainArchitecture.TestShared.Fakes.Common.Enumerations;
using Organization.Application.Features.Invitation.Commands.DeclineInvitation;
using Organization.Domain.Aggregates.InvitationAggregate.Enumerations;
using Organization.Domain.Resources;
using Organization.EndToEndTests.Abstractions;
using Organization.Persistence.Persistence;
using Organization.TestShared.Fakes.Invitation.Requests;
using Organization.TestShared.Helpers;
using Xunit.Abstractions;

namespace Organization.EndToEndTests.Endpoints.Invitation.DeclineInvitation;

/// <summary>
/// Represents the end-to-end tests for the DeclineInvitationEndpoint class.
/// </summary>
public class DeclineInvitationEndpointTests : OrganizationEndToEndTestBase
{
    #region Constructor

    private readonly DeclineInvitationCommand _command;
    private readonly string _mainRoute;

    /// <summary>
    /// Initializes a new instance of the <see cref="DeclineInvitationEndpointTests"/> class.
    /// </summary>
    /// <param name="sharedFixture">The shared fixture.</param>
    /// <param name="outputHelper">The output helper.</param>
    public DeclineInvitationEndpointTests(
        SharedFixtureWithEfCore<API.Program, OrganizationContext> sharedFixture,
        ITestOutputHelper outputHelper
    )
        : base(sharedFixture, outputHelper)
    {
        _command = new FakeDeclineInvitationCommand().Generate();
        _mainRoute = Constants.Routes.Invitation.DeclineInvitation;
    }

    #endregion

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task DeclineInvitation_WhenRequestIsValid_ShouldReturnOkResult()
    {
        // Arrange
        var (invitation, _, _) = await InvitationHelpers.InsertInvitationForUserAsync(
            SharedFixture,
            e2e: true,
            cancellationToken: CancellationToken
        );

        var command = new DeclineInvitationCommand(invitation);

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.WithRequestId()
            .PostAsJsonAsync(_mainRoute, command, cancellationToken: CancellationToken);

        // Assert
        response.Should().BeOk<bool>();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task DeclineInvitation_WhenDataNotExists_ShouldReturnNotFoundError()
    {
        // Arrange

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.WithRequestId()
            .PostAsJsonAsync(_mainRoute, _command, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotBeNotFound(DomainResource.Invitation);
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task DeclineInvitation_WhenRequestIsNotValid_ShouldReturnValidationBadRequestResult()
    {
        // Arrange
        var request = new FakeInvalidDeclineInvitationCommand().Generate();

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.WithRequestId()
            .PostAsJsonAsync(_mainRoute, request, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotBeValid();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task DeclineInvitation_WhenRequestIdIsEmpty_ShouldReturnBadRequestResult()
    {
        // Arrange
        var httpClient = SharedFixture.NormalUserHttpClient;

        // Act
        var response = await httpClient.PostAsJsonAsync(_mainRoute, _command, cancellationToken: CancellationToken);

        // Assert
        response.Should().NotHaveRequestId();
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task DeclineInvitation_WhenInvitationStatusIsNotPending_ShouldThrowBusinessRuleException()
    {
        // Arrange
        var notPendingStatus = new FakeEnumeration<InvitationStatus>(excludedItems: InvitationStatus.Pending);
        var (invitation, _, _) = await InvitationHelpers.InsertInvitationForUserAsync(
            SharedFixture,
            e2e: true,
            status: notPendingStatus,
            cancellationToken: CancellationToken
        );
        var command = _command with { InvitationId = invitation };

        // Act
        var response = await SharedFixture
            .NormalUserHttpClient.WithRequestId()
            .PostAsJsonAsync(_mainRoute, command, cancellationToken: CancellationToken);

        // Assert
        response.Should().BeAgainstBusinessRule(DomainResource.Invitation_Change_Status_Error_Message);
    }

    [Fact]
    [CategoryTrait(TestCategory.EndToEnd)]
    public async Task DeclineInvitation_WhenUserIsNotAuthorized_ShouldReturnUnauthorizedResult()
    {
        // Arrange
        var httpClient = SharedFixture.GuestClient;

        // Act
        var response = await httpClient.PostAsJsonAsync(_mainRoute, _command, cancellationToken: CancellationToken);

        // Assert
        response.Should().Be401Unauthorized();
    }
}
