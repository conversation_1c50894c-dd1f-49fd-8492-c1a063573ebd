using AutoBogus;
using BuildingBlocks.Core.IdGenerator;
using Organization.Application.Features.Company.Commands.RestoreCompany;

namespace Organization.TestShared.Fakes.Company.Requests;

/// <summary>
/// Represents a fake restore company command request.
/// </summary>
public sealed class FakeRestoreCompanyCommandRequest : AutoFaker<RestoreCompanyCommandRequest>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeRestoreCompanyCommandRequest"/> class.
    /// </summary>
    public FakeRestoreCompanyCommandRequest()
    {
        RuleFor(r => r.CompanyId, f => f.Random.Guid());
    }
}

/// <summary>
/// Represents a fake invalid restore company command request.
/// </summary>
public sealed class FakeInvalidRestoreCompanyCommandRequest : AutoFaker<RestoreCompanyCommandRequest>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidRestoreCompanyCommandRequest"/> class.
    /// </summary>
    public FakeInvalidRestoreCompanyCommandRequest()
    {
        RuleFor(r => r.CompanyId, _ => IdGenerator.EmptyId);
    }
}
