using AutoBogus;
using BuildingBlocks.Core.IdGenerator;
using Organization.Application.Features.MemberUser.Commands.RestoreMemberUser;

namespace Organization.TestShared.Fakes.MemberUser.Requests;

/// <summary>
/// Represents a fake restore member user command.
/// </summary>
public sealed class FakeRestoreMemberUserCommand : AutoFaker<RestoreMemberUserCommand>;

/// <summary>
/// Represents a fake restore member user command.
/// </summary>
public sealed class FakeInvalidRestoreMemberUserCommand : AutoFaker<RestoreMemberUserCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidRestoreMemberUserCommand"/> class.
    /// </summary>
    public FakeInvalidRestoreMemberUserCommand()
    {
        RuleFor(x => x.MemberUserId, _ => IdGenerator.EmptyId);
    }
}
