using Bogus;
using BuildingBlocks.Core.Common.Maybe;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds;
using MainArchitecture.Domain.SharedKernel.ValueObjects;
using NSubstitute;
using Organization.Domain.Aggregates.InvitationAggregate.Services;
using Organization.Domain.Aggregates.MemberUserAggregate.Services;
using InvitationDomain = Organization.Domain.Aggregates.InvitationAggregate.Entities.Invitation;

namespace Organization.TestShared.Fakes.Invitation.Entities;

/// <summary>
/// Represents a fake invitation entity for unit testing.
/// </summary>
public sealed class FakeInvitation : Faker<InvitationDomain>
{
    private string? _email;
    private string? _fullname;
    private Guid? _ownerId;

    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvitation"/> class.
    /// </summary>
    public FakeInvitation()
    {
        var memberUserUniquenessChecker = Substitute.For<IMemberUserUniquenessCheckerService>();
        memberUserUniquenessChecker
            .IsUnique(Arg.Any<Email>(), Arg.Any<UserId>())
            .Returns(Maybe<bool>.From(value: true));

        var invitationStatusChecker = Substitute.For<IInvitationStatusCheckerService>();
        invitationStatusChecker
            .IsValidToInvite(Arg.Any<Email>(), Arg.Any<UserId>())
            .Returns(Maybe<bool>.From(value: true));

        CustomInstantiator(f =>
        {
            var email = _email ?? f.Internet.Email();
            var ownerId = _ownerId ?? UserId.Create(f.Random.Guid());
            var fullName = _fullname ?? f.Name.FullName();

            return InvitationDomain.Create(
                ownerId,
                fullName,
                email,
                memberUserUniquenessChecker,
                invitationStatusChecker
            );
        });
    }

    /// <summary>
    /// Sets the email.
    /// </summary>
    public FakeInvitation WithEmail(string email)
    {
        _email = email;
        return this;
    }

    /// <summary>
    /// Sets the full name.
    /// </summary>
    public FakeInvitation WithFullName(string fullName)
    {
        _fullname = fullName;
        return this;
    }

    /// <summary>
    /// Sets the owner identifier.
    /// </summary>
    public FakeInvitation WithOwnerId(Guid ownerId)
    {
        _ownerId = ownerId;
        return this;
    }
}
