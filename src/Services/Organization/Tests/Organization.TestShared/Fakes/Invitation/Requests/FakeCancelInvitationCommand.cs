using AutoBogus;
using BuildingBlocks.Core.IdGenerator;
using Organization.Application.Features.Invitation.Commands.CancelInvitation;

namespace Organization.TestShared.Fakes.Invitation.Requests;

/// <summary>
/// Represents a fake cancel invitation command.
/// </summary>
public sealed class FakeCancelInvitationCommand : AutoFaker<CancelInvitationCommand>;

/// <summary>
/// Represents a fake invalid cancel invitation command.
/// </summary>
public sealed class FakeInvalidCancelInvitationCommand : AutoFaker<CancelInvitationCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidCancelInvitationCommand"/> class.
    /// </summary>
    public FakeInvalidCancelInvitationCommand()
    {
        RuleFor(x => x.InvitationId, _ => IdGenerator.EmptyId);
    }
}
