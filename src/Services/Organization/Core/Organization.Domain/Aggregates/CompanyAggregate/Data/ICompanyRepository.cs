using BuildingBlocks.DDD.SeedWork.Data;
using MainArchitecture.Domain.SharedKernel.StronglyTypedIds.Organization;
using Organization.Domain.Aggregates.CompanyAggregate.Entities;

namespace Organization.Domain.Aggregates.CompanyAggregate.Data;

/// <summary>
/// Represents a repository interface for the <see cref="Company"/> aggregate.
/// </summary>
public interface ICompanyRepository : IRepository<Company, CompanyId>;
