using BuildingBlocks.DDD.SeedWork.Primitives;
using Organization.Domain.Aggregates.InvitationAggregate.Enumerations;
using Organization.Domain.Resources;

namespace Organization.Domain.Aggregates.InvitationAggregate.Rules;

/// <summary>
/// Represents a business rule that checks if the invitation status is still pending.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="InvitationStatusMustNotChangeExceptInPendingStatusRule"/> class.
/// </remarks>
/// <param name="status">The status of the invitation to check.</param>
internal sealed class InvitationStatusMustNotChangeExceptInPendingStatusRule(int status) : IBusinessRule
{
    /// <inheritdoc />
    public bool IsBroken() => status != InvitationStatus.Pending;

    /// <inheritdoc />
    public string Message => DomainResource.Invitation_Change_Status_Error_Message;
}
