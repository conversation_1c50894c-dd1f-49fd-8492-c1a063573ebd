using BuildingBlocks.CQRS.Commands;

namespace Organization.Application.Features.Branch.Commands.RestoreBranch;

/// <summary>
/// This command is handled by <see cref="RestoreBranchCommandHandler"/> class.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="RestoreBranchCommand"/> class.
/// </remarks>
/// <param name="BranchId">The ID of the branch to restore.</param>
public sealed record RestoreBranchCommand(Guid BranchId) : IdempotentCommand<bool>;
