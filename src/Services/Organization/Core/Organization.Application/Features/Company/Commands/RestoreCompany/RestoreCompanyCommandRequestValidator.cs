using BuildingBlocks.Core.Resources;
using BuildingBlocks.Core.Types.Extensions;
using BuildingBlocks.Validation.Extensions;
using FluentValidation;
using MainArchitecture.Domain.Resources;
using Microsoft.Extensions.Logging;

namespace Organization.Application.Features.Company.Commands.RestoreCompany;

/// <summary>
/// Represents the validator for the <see cref="RestoreCompanyCommandRequest"/> class.
/// </summary>
internal sealed class RestoreCompanyCommandRequestValidator : AbstractValidator<RestoreCompanyCommandRequest>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="RestoreCompanyCommandRequestValidator"/> class.
    /// </summary>
    /// <param name="logger">The logger.</param>
    public RestoreCompanyCommandRequestValidator(ILogger<RestoreCompanyCommandRequestValidator> logger)
    {
        RuleFor(r => r.CompanyId).ValidateGuid(SharedResource.Entity_Id.FormatWithStr(ProjectResource.Company));

        if (logger.IsEnabled(LogLevel.Trace))
        {
            logger.LogTrace("INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}
