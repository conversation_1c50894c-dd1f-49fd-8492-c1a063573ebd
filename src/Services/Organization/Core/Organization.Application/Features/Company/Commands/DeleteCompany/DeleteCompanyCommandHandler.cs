using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.CQRS.Commands;
using BuildingBlocks.Security.Jwt.Services;
using MainArchitecture.Domain.Resources;
using Organization.Domain.Aggregates.CompanyAggregate.Data;
using Organization.Domain.Aggregates.CompanyAggregate.Specifications;

namespace Organization.Application.Features.Company.Commands.DeleteCompany;

/// <summary>
/// Handles the processing of the <see cref="DeleteCompanyCommand"/> command.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="DeleteCompanyCommandHandler"/> class.
/// </remarks>
/// <param name="companyRepository">The repository for accessing company data.</param>
/// <param name="userAccessor">The accessor for retrieving user information.</param>
internal sealed class DeleteCompanyCommandHandler(ICompanyRepository companyRepository, IUserAccessor userAccessor)
    : ICommandHandler<DeleteCompanyCommand, bool>
{
    /// <inheritdoc />
    public async Task<Result<bool>> Handle(DeleteCompanyCommand request, CancellationToken cancellationToken)
    {
        var spec = new CompanyByIdAndOwnerSpec(
            request.CompanyId,
            ownerId: userAccessor.GetUserIdentity,
            isReadOnly: false
        );
        var company = await companyRepository.FirstOrDefaultAsync(spec, cancellationToken);
        if (company is null)
        {
            return SharedErrorMessages.NotFoundErrorMessage(ProjectResource.Company);
        }

        company.Delete();

        companyRepository.Update(company);
        return await companyRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
    }
}
