using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Core.Common.Maybe;
using BuildingBlocks.Core.Resources;
using BuildingBlocks.CQRS.Queries;
using BuildingBlocks.Security.Jwt.Services;

namespace Organization.Application.Features.Person.Queries.GetPersonById;

/// <summary>
/// This query handler is responsible for handling <see cref="GetPersonByIdQuery"/> query.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="GetPersonByIdQueryHandler"/> class.
/// </remarks>
/// <param name="databaseQuery">The database query.</param>
/// <param name="userAccessor">The service for accessing identity data.</param>
internal sealed class GetPersonByIdQueryHandler(IGetPersonByIdDatabaseQuery databaseQuery, IUserAccessor userAccessor)
    : IQueryHandler<GetPersonByIdQuery, GetPersonByIdQueryResponse>
{
    /// <inheritdoc />
    public async Task<Maybe<GetPersonByIdQueryResponse>> Handle(
        GetPersonByIdQuery request,
        CancellationToken cancellationToken
    )
    {
        var ownerId = userAccessor.GetUserIdentity;
        var result = await databaseQuery.Handle(request, ownerId, cancellationToken);
        if (result.HasNoValue)
        {
            return SharedErrorMessages.NotFoundErrorMessage(SharedResource.Person);
        }

        return result;
    }
}
