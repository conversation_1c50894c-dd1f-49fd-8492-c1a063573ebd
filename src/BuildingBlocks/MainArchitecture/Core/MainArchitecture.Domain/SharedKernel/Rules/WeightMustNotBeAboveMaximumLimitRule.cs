using BuildingBlocks.Core.Constants;
using BuildingBlocks.DDD.SeedWork.Primitives;
using MainArchitecture.Domain.Resources;

namespace MainArchitecture.Domain.SharedKernel.Rules;

/// <summary>
/// Business rule to ensure weight does not exceed a maximum limit.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="WeightMustNotBeAboveMaximumLimitRule"/> class.
/// </remarks>
/// <param name="value">The value.</param>
internal sealed class WeightMustNotBeAboveMaximumLimitRule(decimal value) : IBusinessRule
{
    /// <inheritdoc />
    public bool IsBroken() => value > GlobalDomainConstValues.WeightMaxWeight;

    /// <inheritdoc />
    public string Message => ProjectResource.Max_Weight_Error_Message;
}
