using BuildingBlocks.Core.Resources;
using BuildingBlocks.DDD.SeedWork.Primitives;
using MainArchitecture.Domain.SharedKernel.ValueObjects;

namespace MainArchitecture.Domain.SharedKernel.Rules;

/// <summary>
/// Represents the money must have the same currency rule.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="MoneyMustHaveTheSameCurrencyRule"/> class.
/// </remarks>
/// <param name="left">The left value.</param>
/// <param name="right">The right value.</param>
internal sealed class MoneyMustHaveTheSameCurrencyRule(MoneyValue left, MoneyValue right) : IBusinessRule
{
    /// <inheritdoc />
    public bool IsBroken() => left.Currency != right.Currency;

    /// <inheritdoc />
    public string Message => SharedResource.Money_Currency_Error_Message;
}
