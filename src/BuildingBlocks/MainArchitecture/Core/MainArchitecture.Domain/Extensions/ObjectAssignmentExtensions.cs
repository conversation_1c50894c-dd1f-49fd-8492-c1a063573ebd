using BuildingBlocks.Core.Exception;

namespace MainArchitecture.Domain.Extensions;

/// <summary>
/// Extension methods for value objects.
/// </summary>
public static class ObjectAssignmentExtensions
{
    /// <summary>
    /// Creates a hash set from the specified value.
    /// </summary>
    /// <typeparam name="T">The type of value object.</typeparam>
    /// <param name="value">The value to create a hash set from.</param>
    /// <returns>The hash set created from the specified value.</returns>
    public static HashSet<T> CreateHashSet<T>(this IReadOnlyCollection<T> value)
    {
        Ensure.NotNull(value, nameof(value));

        return [.. value];
    }

    /// <summary>
    /// Handles the assignment of value objects.
    /// </summary>
    /// <typeparam name="T">The type of value object.</typeparam>
    /// <param name="currentList">The current list of value objects.</param>
    /// <param name="assigned">The list of assigned value objects.</param>
    /// <param name="unassigned">The list of unassigned value objects.</param>
    public static void HandleAssignment<T>(
        this List<T> currentList,
        IReadOnlyCollection<T> assigned,
        IReadOnlyCollection<T> unassigned
    )
    {
        var unassignedSet = unassigned.CreateHashSet();
        var assignedSet = assigned.CreateHashSet();

        currentList.RemoveAll(unassignedSet.Contains);

        var currentSet = currentList.CreateHashSet();

        currentList.AddRange(assignedSet.Distinct().Where(cb => !currentSet.Contains(cb)));
    }

    /// <summary>
    /// Handles the assignment of value objects.
    /// </summary>
    /// <param name="currentList">The current list of value objects.</param>
    /// <param name="newList">The list of new value objects.</param>
    public static void HandleAssignment<T>(this List<T> currentList, IReadOnlyCollection<T> newList)
    {
        Ensure.NotNull(newList, nameof(newList));

        currentList.Clear();
        currentList.AddRange(newList.Distinct());
    }
}
