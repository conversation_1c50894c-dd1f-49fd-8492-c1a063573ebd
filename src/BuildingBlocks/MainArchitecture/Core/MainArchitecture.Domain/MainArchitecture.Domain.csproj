<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <IsPackable>true</IsPackable>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\BuildingBlocks.Core\BuildingBlocks.Core.csproj" />
    <ProjectReference Include="..\..\..\BuildingBlocks.DDD\BuildingBlocks.DDD.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Resources\ProjectResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ProjectResource.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Resources\ProjectResource.fa-IR.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\ProjectResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ProjectResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <InternalsVisibleTo Include="MainArchitecture.UnitTests" />
  </ItemGroup>
</Project>
