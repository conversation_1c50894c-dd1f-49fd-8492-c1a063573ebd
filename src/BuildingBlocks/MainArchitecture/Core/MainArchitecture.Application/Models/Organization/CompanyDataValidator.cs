using BuildingBlocks.Core.Resources;
using BuildingBlocks.Core.Types.Extensions;
using BuildingBlocks.Validation.Extensions;
using FluentValidation;
using MainArchitecture.Domain.Constants;
using MainArchitecture.Domain.Resources;

namespace MainArchitecture.Application.Models.Organization;

/// <summary>
/// Validates the <see cref="CompanyData"/> before it's processed by command validators.
/// </summary>
public sealed class CompanyDataValidator : AbstractValidator<CompanyData>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="CompanyDataValidator"/> class.
    /// </summary>
    public CompanyDataValidator()
    {
        RuleFor(r => r.CompanyId)
            .ValidateGuid(resourceName: SharedResource.Entity_Id.FormatWithStr(ProjectResource.Company));

        RuleFor(r => r.CompanyName)
            .ValidateText(
                resourceName: ProjectResource.Company,
                maxLength: ProjectDomainConstValues.CompanyNameMaxLength
            );
    }
}
