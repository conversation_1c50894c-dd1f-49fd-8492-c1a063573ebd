using BuildingBlocks.Core.Resources;
using BuildingBlocks.Validation.Extensions;
using FluentValidation;

namespace MainArchitecture.Application.Models.Organization;

/// <summary>
/// Validates the <see cref="IPersonDeleteStatusDto"/> before it's processed by command validators.
/// </summary>
public sealed class PersonDeleteStatusDtoValidator : AbstractValidator<IPersonDeleteStatusDto>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="PersonDeleteStatusDtoValidator"/> class.
    /// </summary>
    public PersonDeleteStatusDtoValidator()
    {
        RuleFor(r => r.PersonId).ValidateGuid(resourceName: SharedResource.Person);

        RuleFor(r => r.IsDeleted).NotNull();
    }
}
