using AutoBogus;
using Bogus;
using BuildingBlocks.DDD.SeedWork.Primitives;
using MainArchitecture.Application.Models.FilteringAndPagination;

namespace MainArchitecture.TestShared.Fakes.Common.Models;

/// <summary>
/// Represents a fake filter DTO.
/// </summary>
public sealed class FakeFilterDto : Faker<FilterDto>
{
    private int? _page = FilterDto.DefaultPageNumber;
    private int? _pageSize = FilterDto.PageSizeMaxLength;
    private string? _orderBy = FilterDto.DefaultOrderingValue;
    private bool? _orderByDesc = FilterDto.DefaultOrderingDirection;
    private string? _search = string.Empty;

    /// <summary>
    /// Initializes a new instance of the <see cref="FakeFilterDto"/> class.
    /// </summary>
    public FakeFilterDto()
    {
        CustomInstantiator(f =>
        {
            var page = _page ?? f.Random.Int(1, 100);
            var pageSize = _pageSize ?? f.Random.Int(1, FilterDto.PageSizeMaxLength);
            var orderBy = _orderBy ?? nameof(Entity.CreateDate);
            var orderByDesc = _orderByDesc ?? f.Random.Bool();
            var search = _search ?? f.Lorem.Word();

            return new FilterDto
            {
                Page = page,
                PageSize = pageSize,
                OrderBy = orderBy,
                OrderByDesc = orderByDesc,
                Search = search,
            };
        });
    }

    /// <summary>
    /// Sets the page number.
    /// </summary>
    public FakeFilterDto WithPage(int page)
    {
        _page = page;
        return this;
    }

    /// <summary>
    /// Sets the page size.
    /// </summary>
    public FakeFilterDto WithPageSize(int pageSize)
    {
        _pageSize = pageSize;
        return this;
    }

    /// <summary>
    /// Sets the order by field.
    /// </summary>
    public FakeFilterDto WithOrderBy(string orderBy)
    {
        _orderBy = orderBy;
        return this;
    }

    /// <summary>
    /// Sets the order by descending flag.
    /// </summary>
    public FakeFilterDto WithOrderByDesc(bool orderByDesc)
    {
        _orderByDesc = orderByDesc;
        return this;
    }

    /// <summary>
    /// Sets the search term.
    /// </summary>
    public FakeFilterDto WithSearch(string search)
    {
        _search = search;
        return this;
    }
}

/// <summary>
/// Represents a fake invalid filter DTO.
/// </summary>
public sealed class FakeInvalidFilterDto : AutoFaker<FilterDto>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidFilterDto"/> class.
    /// </summary>
    public FakeInvalidFilterDto()
    {
        RuleFor(x => x.Page, _ => -1);
        RuleFor(x => x.PageSize, _ => FilterDto.PageSizeMaxLength + 1);
    }
}
