using AutoBogus;
using MainArchitecture.Application.Models.Common;

namespace MainArchitecture.TestShared.Fakes.Common.Models;

/// <summary>
/// Represents a fake numeric option view model.
/// </summary>
public sealed class FakeNumericOptionVm : AutoFaker<NumericOptionVm>;

/// <summary>
/// Represents a fake invalid numeric option view model.
/// </summary>
public sealed class FakeInvalidNumericOptionVm : AutoFaker<NumericOptionVm>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeInvalidNumericOptionVm"/> class.
    /// </summary>
    public FakeInvalidNumericOptionVm()
    {
        RuleFor(x => x.Id, _ => -1);
        RuleFor(x => x.Text, _ => string.Empty);
    }
}
