using AutoBogus;
using MainArchitecture.Application.Models.Common;
using MainArchitecture.Domain.SharedKernel.Enumerations;
using MainArchitecture.TestShared.Fakes.Common.Enumerations;

namespace MainArchitecture.TestShared.Fakes.Common.Models;

/// <summary>
/// Represents a fake telephone view model.
/// </summary>
public sealed class FakeTelephoneVm : AutoFaker<TelephoneVm>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeTelephoneVm"/> class.
    /// </summary>
    public FakeTelephoneVm()
    {
        RuleFor(x => x.CountryCode, _ => new FakeEnumeration<ContactCountryCode>().Generate().Code);
    }
}
