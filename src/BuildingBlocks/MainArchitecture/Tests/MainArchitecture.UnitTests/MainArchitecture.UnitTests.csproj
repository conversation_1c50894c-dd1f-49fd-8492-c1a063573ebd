<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\BuildingBlocks.EventSourcing\BuildingBlocks.EventSourcing.csproj" />
    <ProjectReference Include="..\..\..\Tests\BuildingBlocks.Tests.UnitTests\BuildingBlocks.Tests.UnitTests.csproj" />
    <ProjectReference Include="..\MainArchitecture.TestShared\MainArchitecture.TestShared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="xunit.runner.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
