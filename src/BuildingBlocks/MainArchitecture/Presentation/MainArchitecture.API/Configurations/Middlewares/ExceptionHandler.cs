using BuildingBlocks.Core.Exception.Types;
using BuildingBlocks.DDD.SeedWork.Exceptions;
using BuildingBlocks.Validation.Common;
using BuildingBlocks.Web.Problems;
using Grpc.Core;
using MainArchitecture.API.Configurations.Problems;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProblemDetails = Microsoft.AspNetCore.Mvc.ProblemDetails;

namespace MainArchitecture.API.Configurations.Middlewares;

/// <summary>
/// Handles exceptions in web requests by providing standardized API responses.
/// </summary>
/// <param name="problemDetailsService">The problem details service.</param>
/// <param name="logger">The logger.</param>
internal sealed class ExceptionHandler(IProblemDetailsService problemDetailsService, ILogger<ExceptionHandler> logger)
    : IExceptionHandler
{
    /// <inheritdoc />
    public async ValueTask<bool> TryHandleAsync(
        HttpContext httpContext,
        Exception exception,
        CancellationToken cancellationToken
    )
    {
        (ProblemDetails ProblemDetails, int Status) details = exception switch
        {
            BusinessRuleValidationException ruleException => (
                new BusinessRuleExceptionProblemDetails(ruleException),
                StatusCodes.Status409Conflict
            ),
            DomainException domainException => (
                new DomainExceptionProblemDetails(domainException),
                StatusCodes.Status400BadRequest
            ),
            DbUpdateException dbUpdateException => (
                new DataBaseExceptionProblemDetails(dbUpdateException),
                StatusCodes.Status500InternalServerError
            ),
            ValidationException validationException => (
                new ValidationExceptionProblemDetails(validationException),
                StatusCodes.Status400BadRequest
            ),
            CustomAppException applicationException => (
                new CustomAppExceptionProblemDetails(applicationException),
                StatusCodes.Status400BadRequest
            ),
            RpcException rpcException => (
                new RpcExceptionProblemDetails(rpcException),
                StatusCodes.Status400BadRequest
            ),
            _ => (new UnknownExceptionProblemDetails(exception), StatusCodes.Status500InternalServerError),
        };

        logger.LogError(exception, "[Global AppError Handler] - {Problem}", details.ProblemDetails);

        httpContext.Response.StatusCode = details.Status;
        await problemDetailsService.TryWriteAsync(
            new ProblemDetailsContext
            {
                HttpContext = httpContext,
                Exception = exception,
                ProblemDetails = details.ProblemDetails,
            }
        );
        return true;
    }
}
