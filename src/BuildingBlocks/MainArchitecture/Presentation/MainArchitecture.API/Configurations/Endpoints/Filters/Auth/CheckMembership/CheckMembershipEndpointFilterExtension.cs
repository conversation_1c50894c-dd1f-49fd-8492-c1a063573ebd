using BuildingBlocks.Caching.Common;
using BuildingBlocks.Caching.Options;
using BuildingBlocks.Caching.Services;
using BuildingBlocks.Security.Jwt.Services;
using MainArchitecture.Application.Contracts.Infrastructure;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace MainArchitecture.API.Configurations.Endpoints.Filters.Auth.CheckMembership;

/// <summary>
/// Custom endpoint filter to check user membership.
/// </summary>
public static class CheckMembershipEndpointFilterExtension
{
    /// <summary>
    /// Adds the custom endpoint filter to check user membership.
    /// </summary>
    /// <typeparam name="TBuilder">The type of the <see cref="IEndpointConventionBuilder"/> to configure.</typeparam>
    /// <param name="builder">The route type builder.</param>
    /// <param name="needOrganizationInfo">The flag indicating if the endpoint needs organization info.</param>
    /// <returns>The route type builder that can be used to configure the endpoint.</returns>
    public static TBuilder UseCheckMembership<TBuilder>(this TBuilder builder, bool needOrganizationInfo)
        where TBuilder : IEndpointConventionBuilder
    {
        builder.AddEndpointFilterFactory(
            (context, next) =>
                async invocationContext =>
                {
                    await using var scope = context.ApplicationServices.CreateAsyncScope();
                    var scopeServices = scope.ServiceProvider;

                    var filter = new CheckMembershipEndpointFilter(
                        needOrganizationInfo: needOrganizationInfo,
                        cachingService: scopeServices.GetRequiredKeyedService<ICachingService>(
                            nameof(CacheType.Hybrid)
                        ),
                        cacheSettings: scopeServices.GetRequiredService<IOptions<CacheSettings>>().Value,
                        accessService: scopeServices.GetRequiredService<IAccessService>(),
                        organizationAccessor: scopeServices.GetRequiredService<IOrganizationAccessor>(),
                        userAccessor: scopeServices.GetRequiredService<IUserAccessor>()
                    );

                    return await filter.InvokeAsync(invocationContext, next);
                }
        );

        return builder;
    }
}
