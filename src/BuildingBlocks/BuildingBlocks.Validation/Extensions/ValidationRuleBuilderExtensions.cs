using System.Globalization;
using System.Numerics;
using BuildingBlocks.Core.Common.Errors;
using BuildingBlocks.Core.Constants;
using BuildingBlocks.Core.Resources;
using BuildingBlocks.DDD.SeedWork.Primitives;
using FluentValidation;

namespace BuildingBlocks.Validation.Extensions;

/// <summary>
/// Contains the extension methods for the <see cref="IRuleBuilder{T,TProperty}"/> interface.
/// </summary>
public static class ValidationRuleBuilderExtensions
{
    /// <summary>
    /// Validates the range.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <typeparam name="TProperty">The type of the property to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="resourceName">The name of the resource.</param>
    /// <param name="min">The minimum.</param>
    /// <param name="max">The maximum.</param>
    public static IRuleBuilder<T, TProperty> ValidateRange<T, TProperty>(
        this IRuleBuilder<T, TProperty> ruleBuilder,
        string resourceName,
        int min,
        int max
    )
        where TProperty : struct, IComparable, IComparable<TProperty>, IConvertible, IEquatable<TProperty>, IFormattable
    {
        return ruleBuilder
            .NotNull()
            .WithMessage(SharedErrorMessages.RequiredMessage(resourceName))
            .GreaterThanOrEqualTo((TProperty)Convert.ChangeType(min, typeof(TProperty), CultureInfo.InvariantCulture))
            .WithMessage(SharedErrorMessages.GreaterThanOrEqualToMessage(resourceName, min))
            .LessThanOrEqualTo((TProperty)Convert.ChangeType(max, typeof(TProperty), CultureInfo.InvariantCulture))
            .WithMessage(SharedErrorMessages.LessThanOrEqualToMessage(resourceName, max));
    }

    /// <summary>
    /// Validates the id.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <typeparam name="TProperty">The type of the property to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="resourceName">The name of the resource.</param>
    public static IRuleBuilder<T, TProperty> ValidateNaturalNumber<T, TProperty>(
        this IRuleBuilder<T, TProperty> ruleBuilder,
        string resourceName
    )
        where TProperty : struct,
            IComparable,
            IComparable<TProperty>,
            IConvertible,
            IEquatable<TProperty>,
            IFormattable,
            INumber<TProperty>
    {
        return ruleBuilder
            .Must(value => value > TProperty.Zero)
            .WithMessage(
                SharedErrorMessages.GreaterThanOrEqualToMessage(
                    resourceName,
                    GlobalDomainConstValues.NaturalNumberMinue
                )
            );
    }

    /// <summary>
    /// Validates the text.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="resourceName">The name of the resource.</param>
    /// <param name="maxLength">The maximum length.</param>
    /// <param name="minLength">The minimum length.</param>
    /// <param name="nullable">Indicates if the text can be null.</param>
    /// <returns>The rule builder instance.</returns>
    public static IRuleBuilder<T, string?> ValidateText<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        string resourceName,
        int maxLength = 0,
        int minLength = 0,
        bool nullable = false
    )
    {
        if (!nullable)
        {
            ruleBuilder.NotEmpty().WithMessage(SharedErrorMessages.RequiredMessage(resourceName)).NotNull();
        }
        else
        {
            ruleBuilder
                .Must(text => text == null || !string.IsNullOrWhiteSpace(text))
                .WithMessage(SharedErrorMessages.RequiredMessage(resourceName));
        }

        if (maxLength > 0)
        {
            ruleBuilder
                .Must(text => text == null || text.Length <= maxLength)
                .WithMessage(SharedErrorMessages.MaxLengthMessage(resourceName, maxLength));
        }

        if (minLength > 0)
        {
            ruleBuilder
                .Must(text => text == null || text.Length >= minLength)
                .WithMessage(SharedErrorMessages.MinLengthMessage(resourceName, minLength));
        }

        return ruleBuilder;
    }

    /// <summary>
    /// Validates the email.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="nullable">Indicates if the email can be null.</param>
    public static IRuleBuilder<T, string?> ValidateEmail<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        bool nullable = false
    )
    {
        return ruleBuilder
            .ValidateText(
                resourceName: SharedResource.Email,
                minLength: GlobalDomainConstValues.ContactEmailMinLength,
                maxLength: GlobalDomainConstValues.ContactEmailMaxLength,
                nullable: nullable
            )
            .EmailAddress()
            .WithMessage(SharedErrorMessages.ValidationMessage(SharedResource.Email));
    }

    /// <summary>
    /// Validates the first name.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="nullable">Indicates if the first name can be null.</param>
    public static IRuleBuilder<T, string?> ValidateFirstName<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        bool nullable = false
    )
    {
        return ruleBuilder.ValidateText(
            resourceName: SharedResource.First_Name,
            maxLength: GlobalDomainConstValues.ContactFirstNameMaxLength,
            nullable: nullable
        );
    }

    /// <summary>
    /// Validates the last name.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="nullable">Indicates if the last name can be null.</param>
    public static IRuleBuilder<T, string?> ValidateLastName<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        bool nullable = false
    )
    {
        return ruleBuilder.ValidateText(
            resourceName: SharedResource.Last_Name,
            maxLength: GlobalDomainConstValues.ContactLastNameMaxLength,
            nullable: nullable
        );
    }

    /// <summary>
    /// Validates the area code.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="nullable">Indicates if the area code can be null.</param>
    public static IRuleBuilder<T, string?> ValidateAreaCode<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        bool nullable = false
    )
    {
        return ruleBuilder.ValidateText(
            resourceName: SharedResource.Area_Code,
            maxLength: GlobalDomainConstValues.ContactAreaCodeMaxLength,
            minLength: GlobalDomainConstValues.ContactAreaCodeMinLength,
            nullable: nullable
        );
    }

    /// <summary>
    /// Validates the local number.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="nullable">Indicates if the local number can be null.</param>
    public static IRuleBuilder<T, string?> ValidateTelephoneLocalNumber<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        bool nullable = false
    )
    {
        return ruleBuilder.ValidateText(
            resourceName: SharedResource.Local_Number,
            maxLength: GlobalDomainConstValues.ContactTelephoneLocalNumberMaxLength,
            minLength: GlobalDomainConstValues.ContactTelephoneLocalNumberMinLength,
            nullable: nullable
        );
    }

    /// <summary>
    /// Validates the mobile number.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="nullable">Indicates if the mobile number can be null.</param>
    public static IRuleBuilder<T, string?> ValidateMobileNumber<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        bool nullable = false
    )
    {
        return ruleBuilder.ValidateText(
            resourceName: SharedResource.Mobile,
            maxLength: GlobalDomainConstValues.ContactMobileNumberMaxLength,
            minLength: GlobalDomainConstValues.ContactMobileNumberMinLength,
            nullable: nullable
        );
    }

    /// <summary>
    /// Validates the money value.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    public static IRuleBuilder<T, decimal> ValidateMoneyValue<T>(this IRuleBuilder<T, decimal> ruleBuilder)
    {
        return ruleBuilder.ValidateNaturalNumber(SharedResource.Money_Value);
    }

    /// <summary>
    /// Validates the national code.
    /// </summary>
    /// <typeparam name="T">The types of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="nullable">Indicates if the national code can be null.</param>
    public static IRuleBuilder<T, string?> ValidateNationalCode<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        bool nullable = false
    )
    {
        return ruleBuilder.ValidateText(
            resourceName: SharedResource.National_Code,
            maxLength: GlobalDomainConstValues.ContactNationalCodeMaxLength,
            minLength: GlobalDomainConstValues.ContactNationalCodeMinLength,
            nullable: nullable
        );
    }

    /// <summary>
    /// Validates the password.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="nullable">Indicates if the password can be null.</param>
    public static IRuleBuilder<T, string?> ValidatePassword<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        bool nullable = false
    )
    {
        return ruleBuilder.ValidateText(
            resourceName: SharedResource.Password,
            maxLength: GlobalDomainConstValues.PasswordMaxLength,
            minLength: GlobalDomainConstValues.PasswordMinLength,
            nullable: nullable
        );
    }

    /// <summary>
    /// Validates the guid value.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="resourceName">The name of the resource.</param>
    public static IRuleBuilder<T, Guid> ValidateGuid<T>(this IRuleBuilder<T, Guid> ruleBuilder, string resourceName)
    {
        return ruleBuilder.NotEmpty().WithMessage(SharedErrorMessages.RequiredMessage(resourceName)).NotNull();
    }

    /// <summary>
    /// Validates the guid value.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="resourceName">The name of the resource.</param>
    public static IRuleBuilder<T, Guid?> ValidateGuid<T>(this IRuleBuilder<T, Guid?> ruleBuilder, string resourceName)
    {
        return ruleBuilder.NotEmpty().WithMessage(SharedErrorMessages.RequiredMessage(resourceName));
    }

    /// <summary>
    /// Validates the date value.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="resourceName">The name of the resource.</param>
    public static IRuleBuilder<T, DateTime> ValidateDate<T>(
        this IRuleBuilder<T, DateTime> ruleBuilder,
        string resourceName
    )
    {
        return ruleBuilder.NotEmpty().WithMessage(SharedErrorMessages.RequiredMessage(resourceName)).NotNull();
    }

    /// <summary>
    /// Validates the percentage value.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="resourceName">The name of the resource.</param>
    public static IRuleBuilder<T, byte> ValidatePercentage<T>(
        this IRuleBuilder<T, byte> ruleBuilder,
        string resourceName
    )
    {
        return ruleBuilder.ValidateRange(resourceName, 0, 100);
    }

    /// <summary>
    /// Validates the date value.
    /// </summary>
    /// <typeparam name="T">The type of the object to validate.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="resourceName">The name of the resource.</param>
    public static IRuleBuilder<T, string?> ValidateNumberOnly<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        string resourceName
    )
    {
        return ruleBuilder
            .NotEmpty()
            .WithMessage(SharedErrorMessages.RequiredMessage(resourceName))
            .Matches(@"^\d+$")
            .WithMessage(SharedErrorMessages.NumberOnlyMessage(resourceName));
    }

    /// <summary>
    /// Validates the id.
    /// </summary>
    /// <typeparam name="T">The type of the object.</typeparam>
    /// <typeparam name="TEnum">The type of the enumeration.</typeparam>
    /// <param name="ruleBuilder">The rule builder.</param>
    /// <param name="resourceName">The name of the resource.</param>
    /// <returns>The rule builder instance.</returns>
    public static IRuleBuilder<T, int> MustBeValidEnumeration<T, TEnum>(
        this IRuleBuilder<T, int> ruleBuilder,
        string resourceName
    )
        where TEnum : Enumeration
    {
        return ruleBuilder
            .ValidateNaturalNumber(resourceName)
            .Must(id => Enumeration.GetAll<TEnum>().Any(e => e.Id == id))
            .WithMessage(SharedErrorMessages.ValidationMessage(resourceName));
    }
}
