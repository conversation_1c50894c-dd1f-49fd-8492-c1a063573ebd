using Microsoft.EntityFrameworkCore;

namespace BuildingBlocks.Messaging.Persistence.EF.Utilities;

/// <summary>
/// Provides a mechanism for executing a series of operations within a transaction with a resilience strategy.
/// </summary>
internal sealed class ResilientTransaction
{
    private readonly DbContext _context;

    /// <summary>
    /// Initializes a new instance of the <see cref="ResilientTransaction"/> class.
    /// </summary>
    /// <param name="context">The database context.</param>
    private ResilientTransaction(DbContext context) =>
        _context = context ?? throw new ArgumentNullException(nameof(context));

    /// <summary>
    /// Creates a new instance of <see cref="ResilientTransaction"/> with the specified database context.
    /// </summary>
    /// <param name="context">The database context.</param>
    /// <returns>A new instance of <see cref="ResilientTransaction"/>.</returns>
    public static ResilientTransaction New(DbContext context) => new(context);

    /// <summary>
    /// Executes the specified action within a transaction, utilizing a resilience strategy.
    /// </summary>
    /// <param name="action">The action to execute.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    /// <remarks>
    /// This method employs an EF Core resiliency strategy when using multiple DbContexts within an explicit BeginTransaction().
    /// Refer to: https://docs.microsoft.com/en-us/ef/core/miscellaneous/connection-resiliency.
    /// </remarks>
    /// <param name="cancellationToken">The cancellation token.</param>
    public Task ExecuteAsync(Func<Task> action, CancellationToken cancellationToken = default)
    {
        var strategy = _context.Database.CreateExecutionStrategy();
        return strategy.ExecuteAsync(async () =>
        {
            await using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
            await action();
            await transaction.CommitAsync(cancellationToken);
        });
    }
}
