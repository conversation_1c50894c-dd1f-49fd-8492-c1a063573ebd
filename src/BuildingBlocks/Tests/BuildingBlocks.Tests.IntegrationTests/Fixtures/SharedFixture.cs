using System.Collections.Concurrent;
using System.Data.Common;
using System.Net.Http.Headers;
using System.Security.Claims;
using AutoBogus;
using AutoMapper;
using Bogus;
using BuildingBlocks.Caching.Common;
using BuildingBlocks.Caching.Extensions;
using BuildingBlocks.Caching.Options;
using BuildingBlocks.Caching.Services;
using BuildingBlocks.Core.Common;
using BuildingBlocks.Core.Common.Maybe;
using BuildingBlocks.Core.Common.Result;
using BuildingBlocks.Core.Constants;
using BuildingBlocks.CQRS.Commands;
using BuildingBlocks.CQRS.Queries;
using BuildingBlocks.CQRS.Services;
using BuildingBlocks.Dapper.Connection;
using BuildingBlocks.DDD.SeedWork.Events;
using BuildingBlocks.EventBus.Masstransit.Options;
using BuildingBlocks.EventBus.Masstransit.Saga;
using BuildingBlocks.Integration.Messaging.Abstractions;
using BuildingBlocks.Integration.Messaging.Services;
using BuildingBlocks.Outbox.Services;
using BuildingBlocks.Persistence.Common;
using BuildingBlocks.Persistence.Extensions;
using BuildingBlocks.Security.Jwt.Services;
using BuildingBlocks.Tests.IntegrationTests.Auth;
using BuildingBlocks.Tests.IntegrationTests.Extensions;
using BuildingBlocks.Tests.IntegrationTests.Factory;
using BuildingBlocks.Tests.IntegrationTests.Helpers;
using FluentAssertions;
using FluentAssertions.Extensions;
using Grpc.Core;
using Grpc.Core.Testing;
using Grpc.Net.Client;
using MainArchitecture.Application.Contracts.Infrastructure;
using MainArchitecture.Application.Models.Organization;
using MainArchitecture.Domain.SharedKernel.ValueObjects.Organization;
using MassTransit;
using MassTransit.Testing;
using MediatR;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NSubstitute;
using NSubstitute.ClearExtensions;
using Quartz;
using Serilog;
using WireMock.Server;
using Xunit.Sdk;

namespace BuildingBlocks.Tests.IntegrationTests.Fixtures;

/// <summary>
/// Represents a shared fixture.
/// </summary>
/// <typeparam name="TEntryPoint">The type of the entry point.</typeparam>
public class SharedFixture<TEntryPoint> : IAsyncLifetime
    where TEntryPoint : class
{
    /// <summary>
    /// The message sink.
    /// </summary>
    private readonly IMessageSink _messageSink;

    /// <summary>
    /// The test harness.
    /// </summary>
    private ITestHarness? _harness;

    /// <summary>
    /// The HTTP context accessor.
    /// </summary>
    private IHttpContextAccessor? _httpContextAccessor;

    /// <summary>
    /// The service provider.
    /// </summary>
    private IServiceProvider? _serviceProvider;

    /// <summary>
    /// The configuration.
    /// </summary>
    private IConfiguration? _configuration;

    /// <summary>
    /// The normal gRPC client.
    /// </summary>
    private HttpClient? _normalGrpcClient;

    /// <summary>
    /// The guest gRPC client.
    /// </summary>
    private HttpClient? _guestGrpcClient;

    /// <summary>
    /// The admin HTTP client.
    /// </summary>
    private HttpClient? _adminClient;

    /// <summary>
    /// The normal HTTP client.
    /// </summary>
    private HttpClient? _normalClient;

    /// <summary>
    /// The guest HTTP client.
    /// </summary>
    private HttpClient? _guestClient;

    /// <summary>
    /// Gets the faker for the test.
    /// </summary>
    public readonly Faker Faker = new();

    /// <summary>
    /// The mock services.
    /// </summary>
    private readonly ConcurrentDictionary<Type, object> _mockServices;

    /// <summary>
    /// The on shared fixture initialized.
    /// </summary>
    public Func<Task>? OnSharedFixtureInitialized;

    /// <summary>
    /// The on shared fixture disposed.
    /// </summary>
    public Func<Task>? OnSharedFixtureDisposed;

    /// <summary>
    /// Gets the logger.
    /// </summary>
    public ILogger Logger { get; }

    /// <summary>
    /// Gets the SQLs container fixture.
    /// </summary>
    public SqlContainerFixture SqlContainerFixture { get; }

    /// <summary>
    /// Gets the Redis container fixture.
    /// </summary>
    public RedisContainerFixture RedisContainerFixture { get; }

    /// <summary>
    /// Gets the RabbitMQ container fixture.
    /// </summary>
    public RabbitMqContainerFixture RabbitMqContainerFixture { get; }

    /// <summary>
    /// Gets the factory.
    /// </summary>
    public CustomWebApplicationFactory<TEntryPoint> Factory { get; private set; }

    /// <summary>
    /// Gets the service provider.
    /// </summary>
    public IServiceProvider ServiceProvider => _serviceProvider ??= Factory.Services;

    /// <summary>
    /// Gets the configuration.
    /// </summary>
    public IConfiguration Configuration => _configuration ??= ServiceProvider.GetRequiredService<IConfiguration>();

    /// <summary>
    /// Gets the MassTransit harness.
    /// </summary>
    public ITestHarness MasstransitHarness => _harness ??= ServiceProvider.GetTestHarness();

    /// <summary>
    /// Gets the HTTP context accessor.
    /// </summary>
    public IHttpContextAccessor HttpContextAccessor =>
        _httpContextAccessor ??= ServiceProvider.GetRequiredService<IHttpContextAccessor>();

    /// <summary>
    /// Gets the HTTP client for the normal client.
    /// </summary>
    public HttpClient NormalGrpcClient
    {
        get
        {
            if (_normalGrpcClient is not null)
            {
                return _normalGrpcClient;
            }

            var claims = CreateNormalUserMock().Claims;
            _normalGrpcClient = Factory.CreateClient();
            _normalGrpcClient.DefaultRequestHeaders.Accept.Add(
                new MediaTypeWithQualityHeaderValue(GlobalApplicationConstValues.Header.ApplicationGrpcContent)
            );
            _normalGrpcClient.SetFakeJwtBearerClaims(claims);

            return _normalGrpcClient;
        }
    }

    /// <summary>
    /// Gets the HTTP client for the guest client.
    /// </summary>
    public HttpClient GuestGrpcClient
    {
        get
        {
            if (_guestGrpcClient is not null)
            {
                return _guestGrpcClient;
            }

            _guestGrpcClient = Factory.CreateClient();
            _guestGrpcClient.DefaultRequestHeaders.Accept.Add(
                new MediaTypeWithQualityHeaderValue(GlobalApplicationConstValues.Header.ApplicationGrpcContent)
            );

            return _guestGrpcClient;
        }
    }

    /// <summary>
    /// Gets we should not dispose this GuestClient, because we reuse it in our tests.
    /// </summary>
    public HttpClient GuestClient
    {
        get
        {
            if (_guestClient is not null)
            {
                return _guestClient;
            }

            _guestClient = Factory.CreateClient();
            _guestClient.DefaultRequestHeaders.Accept.Add(
                new MediaTypeWithQualityHeaderValue(GlobalApplicationConstValues.Header.ApplicationJsonContent)
            );

            return _guestClient;
        }
    }

    /// <summary>
    /// Gets we should not dispose this AdminHttpClient, because we reuse it in our tests.
    /// </summary>
    public HttpClient AdminHttpClient => _adminClient ??= CreateAdminHttpClient();

    /// <summary>
    /// Gets we should not dispose this NormalUserHttpClient, because we reuse it in our tests.
    /// </summary>
    public HttpClient NormalUserHttpClient => _normalClient ??= CreateNormalUserHttpClient();

    /// <summary>
    /// Gets the wire mock server.
    /// </summary>
    public WireMockServer WireMockServer { get; }

    /// <summary>
    /// Gets the wire mock server url.
    /// </summary>
    public string WireMockServerUrl { get; }

    /// <summary>
    /// Gets and sets the normal grpc channel.
    /// </summary>
    public GrpcChannel NormalGrpcChannel =>
        GrpcChannel.ForAddress(NormalGrpcClient.BaseAddress!, new GrpcChannelOptions { HttpClient = NormalGrpcClient });

    /// <summary>
    /// Gets and sets the normal grpc channel.
    /// </summary>
    public GrpcChannel GuestGrpcChannel =>
        GrpcChannel.ForAddress(GuestGrpcClient.BaseAddress!, new GrpcChannelOptions { HttpClient = GuestGrpcClient });

    /// <summary>
    /// Initializes a new instance of the <see cref="SharedFixture{TEntryPoint}"/> class.
    /// </summary>
    /// <param name="messageSink">The message sink.</param>
    public SharedFixture(IMessageSink messageSink)
    {
        _messageSink = messageSink;
        messageSink.OnMessage(new DiagnosticMessage("Constructing SharedFixture..."));
        Logger = new LoggerConfiguration()
            .MinimumLevel.Verbose()
            .WriteTo.TestOutput(messageSink)
            .CreateLogger()
            .ForContext<SharedFixture<TEntryPoint>>();

        SqlContainerFixture = new SqlContainerFixture(messageSink);
        RedisContainerFixture = new RedisContainerFixture(messageSink);
        RabbitMqContainerFixture = new RabbitMqContainerFixture(messageSink);

        _mockServices = new ConcurrentDictionary<Type, object>();

        AutoFaker.Configure(b => b.WithRecursiveDepth(3).WithTreeDepth(1).WithRepeatCount(1));

        AssertionOptions.AssertEquivalencyUsing(options =>
        {
            options
                .Using<DateTime>(ctx => ctx.Subject.Should().BeCloseTo(ctx.Expectation, 1.Seconds()))
                .WhenTypeIs<DateTime>();
            options
                .Using<DateTimeOffset>(ctx => ctx.Subject.Should().BeCloseTo(ctx.Expectation, 1.Seconds()))
                .WhenTypeIs<DateTimeOffset>();

            return options;
        });

        WireMockServer = WireMockServer.Start();
        WireMockServerUrl = WireMockServer.Url!;

        Factory = new CustomWebApplicationFactory<TEntryPoint>();
    }

    /// <inheritdoc />
    public async Task InitializeAsync()
    {
        _messageSink.OnMessage(new DiagnosticMessage("SharedFixture Started..."));

        await Factory.InitializeAsync();
        await SqlContainerFixture.InitializeAsync();
        await RedisContainerFixture.InitializeAsync();
        await RabbitMqContainerFixture.InitializeAsync();

        Factory.AddOverrideEnvKeyValues(
            new Dictionary<string, string>
            {
                {
                    $"{DbConstants.SqlConnectionStringSectionName}:{DbConstants.SqlConnectionStringName}",
                    SqlContainerFixture.Container.GetConnectionString()
                },
                {
                    $"{EventBusSettings.SettingsKey}:{nameof(EventBusSettings.HostAddress)}",
                    $"amqp://{
                        RabbitMqContainerFixture.RabbitMqContainerOptions.UserName
                    }:{
                        RabbitMqContainerFixture.RabbitMqContainerOptions.Password
                    }@{
                        RabbitMqContainerFixture.Container.Hostname
                    }:{
                        RabbitMqContainerFixture.HostPort
                    }"
                },
                {
                    $"{EventBusSettings.SettingsKey}:{nameof(EventBusSettings.ExchangeName)}",
                    RabbitMqContainerFixture.RabbitMqContainerOptions.ExchangeName
                },
                {
                    $"{CacheSettings.SettingsKey}:{nameof(CacheSettings.Host)}",
                    RedisContainerFixture.Container.Hostname
                },
                { $"{CacheSettings.SettingsKey}:{nameof(CacheSettings.Port)}", $"{RedisContainerFixture.HostPort}" },
            }
        );

        Factory.AddOverrideInMemoryConfig(new Dictionary<string, string>(StringComparer.Ordinal));
        Factory.ConfigurationAction += cfg => cfg["WireMockUrl"] = WireMockServerUrl;

        var initCallback = OnSharedFixtureInitialized?.Invoke();
        if (initCallback != null)
        {
            await initCallback;
        }
    }

    /// <inheritdoc />
    public async Task DisposeAsync()
    {
        await SqlContainerFixture.DisposeAsync();
        await RedisContainerFixture.DisposeAsync();
        await RabbitMqContainerFixture.DisposeAsync();
        WireMockServer.Stop();
        AdminHttpClient.Dispose();
        NormalUserHttpClient.Dispose();
        GuestClient.Dispose();

        var disposeCallback = OnSharedFixtureDisposed?.Invoke();
        if (disposeCallback != null)
        {
            await disposeCallback;
        }

        await Factory.DisposeAsync();

        _messageSink.OnMessage(new DiagnosticMessage("SharedFixture Stopped..."));
    }

    /// <summary>
    /// Cleans up the messaging.
    /// </summary>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task CleanupMessaging(CancellationToken cancellationToken = default)
    {
        return RabbitMqContainerFixture.CleanupQueuesAsync(cancellationToken);
    }

    /// <summary>
    /// Resets the databases.
    /// </summary>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task ResetDatabasesAsync(CancellationToken cancellationToken = default)
    {
        await SqlContainerFixture.ResetDbAsync(cancellationToken);
        await RedisContainerFixture.CleanupAsync();
        await ExecuteScopeAsync(sp =>
        {
            var cache = sp.GetService<IMemoryCache>();
            if (cache is MemoryCache memoryCache)
            {
                memoryCache.Compact(1.0);
            }

            return Task.CompletedTask;
        });

        await ExecuteScopeAsync(async scope =>
        {
            var seeders = scope.GetServices<IDbSeeder>();
            foreach (var seeder in seeders)
            {
                await seeder.SeedAsync(cancellationToken);
            }
        });
    }

    /// <summary>
    /// We could use `WithWebHostBuilder` method for specific config and customize existing `CustomWebApplicationFactory`.
    /// </summary>
    /// <param name="builder">The builder.</param>
    /// <returns>The <see cref="CustomWebApplicationFactory{TEntryPoint}"/>.</returns>
    public CustomWebApplicationFactory<TEntryPoint> WithWebHostBuilder(Action<IWebHostBuilder> builder)
    {
        Factory = Factory.WithWebHostBuilder(builder);
        return Factory;
    }

    /// <summary>
    /// Withs the host builder.
    /// </summary>
    /// <param name="builder">The builder.</param>
    /// <returns>The <see cref="CustomWebApplicationFactory{TEntryPoint}"/>.</returns>
    public CustomWebApplicationFactory<TEntryPoint> WithHostBuilder(Action<IHostBuilder> builder)
    {
        Factory = Factory.WithHostBuilder(builder);
        return Factory;
    }

    /// <summary>
    /// Withs the configure app configurations.
    /// </summary>
    /// <param name="cfg">The configuration builder.</param>
    /// <returns>The <see cref="CustomWebApplicationFactory{TEntryPoint}"/>.</returns>
    public CustomWebApplicationFactory<TEntryPoint> WithConfigureAppConfigurations(
        Action<HostBuilderContext, IConfigurationBuilder> cfg
    )
    {
        Factory.WithConfigureAppConfigurations(cfg);
        return Factory;
    }

    /// <summary>
    /// Configures the test configure app.
    /// </summary>
    /// <param name="configBuilder">The configuration builder.</param>
    public void ConfigureTestConfigureApp(Action<HostBuilderContext, IConfigurationBuilder>? configBuilder)
    {
        if (configBuilder is not null)
        {
            Factory.TestConfigureApp += configBuilder;
        }
    }

    /// <summary>
    /// Configures the test services.
    /// </summary>
    /// <param name="services">The services.</param>
    public void ConfigureTestServices(Action<IServiceCollection>? services)
    {
        if (services is not null)
        {
            Factory.TestConfigureServices += services;
        }
    }

    /// <summary>
    /// Sets the output helper.
    /// </summary>
    /// <param name="outputHelper">The output helper.</param>
    public void SetOutputHelper(ITestOutputHelper outputHelper)
    {
        Factory.SetOutputHelper(outputHelper);
    }

    /// <summary>
    /// Sets the user.
    /// </summary>
    /// <param name="isAdmin">The value indicating whether the user is admin or not.</param>
    public void SetUserOnly(bool isAdmin = false)
    {
        new HttpContextBuilder()
            .WithRequestId()
            .WithClaims(isAdmin ? CreateAdminUserMock().Claims : CreateNormalUserMock().Claims)
            .WithNoOrganization()
            .Build(HttpContextAccessor);
    }

    /// <summary>
    /// Sets the user and organization.
    /// </summary>
    /// <param name="isOwning">The value indicating whether the organization is owned or not.</param>
    /// <param name="ownerId">The owner ID.</param>
    public void SetUserAndOrganization(bool isOwning = true, Guid? ownerId = null)
    {
        var owner = isOwning ? GetLoggedInUserId() : ownerId ?? Faker.Random.Guid();
        new HttpContextBuilder()
            .WithNormalUser()
            .WithOrganization(owner, isOwning)
            .WithRequestId()
            .Build(HttpContextAccessor);
    }

    /// <summary>
    /// Creates gRPC metadata with specified configuration.
    /// </summary>
    /// <param name="isOwner">Whether the organization is owner.</param>
    /// <returns>The configured Metadata.</returns>
    public Metadata CreateGrpcMetadata(bool isOwner = true)
    {
        var builder = new GrpcMetadataBuilder().WithRequestId().WithLanguage();
        var userId = GetLoggedInUserId();
        builder.WithOrganization(userId, isOwner);

        return builder.Build();
    }

    /// <summary>
    /// Executes the scope asynchronous.
    /// </summary>
    /// <typeparam name="T">The type of the result.</typeparam>
    /// <param name="action">The action.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task<T> ExecuteScopeAsync<T>(Func<IServiceProvider, T> action)
    {
        await using var scope = ServiceProvider.CreateAsyncScope();
        return action(scope.ServiceProvider);
    }

    /// <summary>
    /// Executes the scope asynchronous.
    /// </summary>
    /// <param name="action">The action.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task ExecuteScopeAsync(Func<IServiceProvider, Task> action)
    {
        await using var scope = ServiceProvider.CreateAsyncScope();
        await action(scope.ServiceProvider);
    }

    /// <summary>
    /// Executes the scope asynchronous.
    /// </summary>
    /// <typeparam name="T">The type of the result.</typeparam>
    /// <param name="action">The action.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task<T> ExecuteScopeAsync<T>(Func<IServiceProvider, Task<T>> action)
    {
        await using var scope = ServiceProvider.CreateAsyncScope();

        var result = await action(scope.ServiceProvider);

        return result;
    }

    /// <summary>
    /// Executes the mapping asynchronous.
    /// </summary>
    /// <typeparam name="TSource">The type of the source.</typeparam>
    /// <typeparam name="TDestination">The type of the destination.</typeparam>
    /// <param name="source">The source.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<TDestination> ExecuteMappingAsync<TSource, TDestination>(TSource source)
        where TSource : class
        where TDestination : class
    {
        return ExecuteScopeAsync<TDestination>(sp =>
        {
            var mapper = sp.GetRequiredService<IMapper>();
            return mapper.Map<TDestination>(source);
        });
    }

    /// <summary>
    /// Executes the specified action within a database connection.
    /// </summary>
    /// <param name="action">The action to execute.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task ExecuteDapperCommandAsync(
        Func<DbConnection, Task> action,
        CancellationToken cancellationToken = default
    )
    {
        return ExecuteScopeAsync(async sp =>
        {
            await using var connection = await sp.GetRequiredService<IDbConnectionFactory>()
                .GetOrCreateConnectionAsync(cancellationToken);
            await action(connection);
        });
    }

    /// <summary>
    /// Gets the user identity.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<Guid> GetUserIdentityAsync()
    {
        return ExecuteScopeAsync(sp =>
        {
            var userAccessor = sp.GetRequiredService<IUserAccessor>();
            return userAccessor.GetUserIdentity;
        });
    }

    /// <summary>
    /// Gets the user email.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<string> GetUserEmailAsync()
    {
        return ExecuteScopeAsync(sp =>
        {
            var userAccessor = sp.GetRequiredService<IUserAccessor>();
            return userAccessor.GetUserEmail;
        });
    }

    /// <summary>
    /// Gets the user email.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<string> GetUserFullNameAsync()
    {
        return ExecuteScopeAsync(sp =>
        {
            var userAccessor = sp.GetRequiredService<IUserAccessor>();
            return userAccessor.GetUserFullName;
        });
    }

    /// <summary>
    /// Get user company branch.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<CompanyBranchData?> GetUserCompanyBranchAsync()
    {
        return ExecuteScopeAsync(sp =>
        {
            var userAccessor = sp.GetRequiredService<IOrganizationAccessor>();
            return userAccessor.GetCompanyBranch;
        });
    }

    /// <summary>
    /// Get user organization ownership.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<OrganizationOwnershipDto?> GetUserOrganizationAsync()
    {
        return ExecuteScopeAsync(sp =>
        {
            var userAccessor = sp.GetRequiredService<IOrganizationAccessor>();
            return userAccessor.GetOrganizationOwnership;
        });
    }

    /// <summary>
    /// Maps the company branches data.
    /// </summary>
    /// <param name="companyBranches">The company branches data.</param>
    /// <returns>The mapped company branches.</returns>
    public Task<IReadOnlyCollection<CompanyBranchData>> MapToCompanyBranchesDataAsync(
        params IReadOnlyCollection<CompanyBranch> companyBranches
    )
    {
        return ExecuteMappingAsync<IReadOnlyCollection<CompanyBranch>, IReadOnlyCollection<CompanyBranchData>>(
            companyBranches
        );
    }

    /// <summary>
    /// Maps the company branches.
    /// </summary>
    /// <param name="companyBranches">The company branches.</param>
    /// <returns>The mapped company branches.</returns>
    public Task<IReadOnlyCollection<CompanyBranch>> MapToCompanyBranchesAsync(
        params IReadOnlyCollection<CompanyBranchData> companyBranches
    )
    {
        return ExecuteMappingAsync<IReadOnlyCollection<CompanyBranchData>, IReadOnlyCollection<CompanyBranch>>(
            companyBranches
        );
    }

    /// <summary>
    /// Sends the asynchronous.
    /// </summary>
    /// <typeparam name="TResponse">The type of the response.</typeparam>
    /// <param name="request">The request.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<TResponse> SendAsync<TResponse>(
        IRequest<TResponse> request,
        CancellationToken cancellationToken = default
    )
    {
        return ExecuteScopeAsync(sp =>
        {
            var mediator = sp.GetRequiredService<IMediator>();

            return mediator.Send(request, cancellationToken);
        });
    }

    /// <summary>
    /// Sends the asynchronous.
    /// </summary>
    /// <typeparam name="TResponse">The type of the response.</typeparam>
    /// <param name="request">The request.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<Result<TResponse>> SendAsync<TResponse>(
        ICommand<TResponse> request,
        CancellationToken cancellationToken = default
    )
        where TResponse : notnull
    {
        return ExecuteScopeAsync(sp =>
        {
            var commandBus = sp.GetRequiredService<IInMemoryBus>();

            return commandBus.Send(request, cancellationToken);
        });
    }

    /// <summary>
    /// Queries the asynchronous.
    /// </summary>
    /// <typeparam name="TResponse">The type of the response.</typeparam>
    /// <param name="query">The query.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<Maybe<TResponse>> QueryAsync<TResponse>(
        IQuery<TResponse> query,
        CancellationToken cancellationToken = default
    )
        where TResponse : notnull
    {
        return ExecuteScopeAsync(sp =>
        {
            var queryProcessor = sp.GetRequiredService<IInMemoryBus>();

            return queryProcessor.Send(query, cancellationToken);
        });
    }

    /// <summary>
    /// Publishes the integration event asynchronous.
    /// </summary>
    /// <typeparam name="TMessage">The type of the message.</typeparam>
    /// <param name="message">The message.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task PublishIntegrationEventAsync<TMessage>(TMessage message, CancellationToken cancellationToken = default)
        where TMessage : IntegrationBaseEvent, IIntegrationMessage
    {
        return ExecuteScopeAsync(async sp =>
        {
            var bus = sp.GetRequiredService<IIntegrationEventService>();

            await bus.PublishEventAsync(message, cancellationToken);
        });
        // return MasstransitHarness.Bus.Publish(message, cancellationToken);
    }

    /// <summary>
    /// Publishes the integration command asynchronous.
    /// </summary>
    /// <typeparam name="TMessage">The type of the message.</typeparam>
    /// <param name="command">The command.</param>
    /// <param name="queueName">Name of the queue.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task PublishIntegrationCommandAsync<TMessage>(
        TMessage command,
        string queueName,
        CancellationToken cancellationToken = default
    )
        where TMessage : IntegrationBaseCommand, IIntegrationMessage
    {
        return ExecuteScopeAsync(async sp =>
        {
            var bus = sp.GetRequiredService<IIntegrationEventService>();

            await bus.SendCommandAsync(command, queueName, cancellationToken);
        });
        // var sendEndpoint = await MasstransitHarness.Bus.GetSendEndpoint(
        //     address: MessagingConstants.GetQueueUri(queueName)
        // );
        // await sendEndpoint.Send(command, cancellationToken);
    }

    /// <summary>
    /// Asserts the eventually that a probe is satisfied.
    /// </summary>
    /// <param name="probe">The probe.</param>
    /// <param name="timeoutSecond">The timeout.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task AssertEventually(IProbe probe, int? timeoutSecond = null, CancellationToken cancellationToken = default)
    {
        var time = timeoutSecond ?? Constants.Timeouts.DefaultTimeoutMs;
        return new Poller(time).CheckAsync(probe, cancellationToken);
    }

    /// <summary>
    /// Waits until condition met.
    /// </summary>
    /// <param name="conditionToMet">The condition to meet.</param>
    /// <param name="timeoutMs">The timeout millisecond.</param>
    /// <param name="exception">The exception to throw.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task<bool> WaitUntilConditionMet(
        Func<Task<bool>> conditionToMet,
        int? timeoutMs = null,
        string? exception = null,
        CancellationToken cancellationToken = default
    )
    {
        var time = timeoutMs ?? Constants.Timeouts.DefaultTimeoutMs;

        var startTime = SystemClock.Now;
        var timeoutExpired = false;
        var meet = await conditionToMet.Invoke();
        while (!meet)
        {
            if (timeoutExpired)
            {
                throw new TimeoutException(
                    exception ?? $"Condition not met for the test in the '{timeoutExpired}' second."
                );
            }

            await Task.Delay(Constants.Timeouts.DefaultPollingIntervalMs, cancellationToken);
            meet = await conditionToMet.Invoke();
            timeoutExpired = SystemClock.Now - startTime > TimeSpan.FromSeconds(time);
        }

        return meet;
    }

    /// <summary>
    /// Waits for publishing.
    /// </summary>
    /// <typeparam name="TMessage">The type of the message.</typeparam>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation with the result.</returns>
    public Task<bool> WaitForPublishing<TMessage>(CancellationToken cancellationToken = default)
        where TMessage : IntegrationBaseEvent, IIntegrationMessage
    {
        return WaitUntilConditionMet(
            () =>
                MasstransitHarness.Published.Any(
                    message =>
                    {
                        var messageFilter = new PublishedMessageFilter();
                        var faultMessageFilter = new PublishedMessageFilter();

                        messageFilter.Includes.Add<TMessage>();
                        faultMessageFilter.Includes.Add<Fault<TMessage>>();

                        var faulty = faultMessageFilter.Any(message);
                        var published = messageFilter.Any(message);

                        return published && !faulty;
                    },
                    cancellationToken: cancellationToken
                ),
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Waits for sending.
    /// </summary>
    /// <typeparam name="TMessage">The type of the message.</typeparam>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation with the result.</returns>
    public Task<bool> WaitForSending<TMessage>(CancellationToken cancellationToken = default)
        where TMessage : IntegrationBaseCommand, IIntegrationMessage
    {
        return WaitUntilConditionMet(
            () =>
                MasstransitHarness.Sent.Any(
                    message =>
                    {
                        var messageFilter = new SentMessageFilter();
                        var faultMessageFilter = new SentMessageFilter();

                        messageFilter.Includes.Add<TMessage>();
                        faultMessageFilter.Includes.Add<Fault<TMessage>>();

                        var faulty = faultMessageFilter.Any(message);
                        var sent = messageFilter.Any(message);

                        return sent && !faulty;
                    },
                    cancellationToken: cancellationToken
                ),
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Waits for consuming.
    /// </summary>
    /// <typeparam name="TMessage">The type of the message.</typeparam>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<bool> WaitForConsuming<TMessage>(CancellationToken cancellationToken = default)
        where TMessage : class, IIntegrationMessage
    {
        return WaitUntilConditionMet(
            () =>
                MasstransitHarness.Consumed.Any(
                    message =>
                    {
                        var messageFilter = new ReceivedMessageFilter();
                        var faultMessageFilter = new ReceivedMessageFilter();

                        messageFilter.Includes.Add<TMessage>();
                        faultMessageFilter.Includes.Add<Fault<TMessage>>();

                        var faulty = faultMessageFilter.Any(message);
                        var consumed = messageFilter.Any(message);

                        return consumed && !faulty;
                    },
                    cancellationToken
                ),
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Waits for consuming.
    /// </summary>
    /// <typeparam name="TMessage">The type of the message.</typeparam>
    /// <typeparam name="TConsumedBy">The type of the consumed by.</typeparam>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<bool> WaitForConsuming<TMessage, TConsumedBy>(CancellationToken cancellationToken = default)
        where TMessage : class, IIntegrationMessage
        where TConsumedBy : class, IConsumer
    {
        var consumerHarness = ServiceProvider.GetRequiredService<IConsumerTestHarness<TConsumedBy>>();

        return WaitUntilConditionMet(
            () =>
                consumerHarness.Consumed.Any(
                    message =>
                    {
                        var messageFilter = new ReceivedMessageFilter();
                        var faultMessageFilter = new ReceivedMessageFilter();

                        messageFilter.Includes.Add<TMessage>();
                        faultMessageFilter.Includes.Add<Fault<TMessage>>();

                        var faulty = faultMessageFilter.Any(message);
                        var consumed = messageFilter.Any(message);

                        return consumed && !faulty;
                    },
                    cancellationToken
                ),
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Gets a saga state machine harness for testing.
    /// </summary>
    /// <typeparam name="TStateMachine">The type of the state machine.</typeparam>
    /// <typeparam name="TInstance">The type of the saga instance.</typeparam>
    /// <returns>The saga state machine test harness.</returns>
    public ISagaStateMachineTestHarness<TStateMachine, TInstance> GetSagaStateMachineHarness<TStateMachine, TInstance>()
        where TStateMachine : MassTransitStateMachine<TInstance>
        where TInstance : BaseSagaStateInstance
    {
        return MasstransitHarness.GetSagaStateMachineHarness<TStateMachine, TInstance>();
    }

    /// <summary>
    /// Checks if a saga exists with the given correlation id and state.
    /// </summary>
    /// <param name="correlationId">The correlation identifier.</param>
    /// <param name="stateSelector">The state selector.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task<bool> SagaExists<TStateMachine, TInstance>(
        Guid correlationId,
        Func<TStateMachine, State> stateSelector
    )
        where TStateMachine : MassTransitStateMachine<TInstance>
        where TInstance : BaseSagaStateInstance
    {
        var harness = GetSagaStateMachineHarness<TStateMachine, TInstance>();
        var instance = await harness.Exists(correlationId, stateSelector);
        return instance.HasValue;
    }

    /// <summary>
    /// Waits until a message is consumed by the saga.
    /// </summary>
    /// <typeparam name="TStateMachine">The type of the state machine.</typeparam>
    /// <typeparam name="TInstance">The type of the saga instance.</typeparam>
    /// <typeparam name="TMessage">The type of the message.</typeparam>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<bool> WaitForSagaConsumed<TStateMachine, TInstance, TMessage>(
        CancellationToken cancellationToken = default
    )
        where TStateMachine : MassTransitStateMachine<TInstance>
        where TInstance : BaseSagaStateInstance
        where TMessage : class
    {
        return WaitUntilConditionMet(
            () =>
            {
                var harness = GetSagaStateMachineHarness<TStateMachine, TInstance>();
                return harness.Consumed.Any<TMessage>(cancellationToken);
            },
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Waits until a saga transitions to the expected state.
    /// </summary>
    /// <param name="correlationId">The correlation identifier.</param>
    /// <param name="stateSelector">The state selector.</param>
    /// <param name="timeoutMs">The timeout in milliseconds.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<bool> WaitForSagaState<TStateMachine, TInstance>(
        Guid correlationId,
        Func<TStateMachine, State> stateSelector,
        int? timeoutMs = null,
        CancellationToken cancellationToken = default
    )
        where TStateMachine : MassTransitStateMachine<TInstance>
        where TInstance : BaseSagaStateInstance
    {
        return WaitUntilConditionMet(
            () => SagaExists<TStateMachine, TInstance>(correlationId, stateSelector),
            timeoutMs: timeoutMs,
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Waits until the outbox message is persisted.
    /// </summary>
    /// <typeparam name="TMessage">The type of the message.</typeparam>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task<bool> ShouldProcessedOutboxPersistMessage<TMessage>(CancellationToken cancellationToken = default)
        where TMessage : class, IDomainEvent
    {
        return WaitUntilConditionMet(
            () =>
            {
                return ExecuteScopeAsync(async scope =>
                {
                    var outboxManager = scope.GetService<IOutboxManager>();
                    ArgumentNullException.ThrowIfNull(outboxManager);

                    var outboxMessages = await outboxManager.GetProcessedOutboxMessages<TMessage>(cancellationToken);
                    return outboxMessages is { Count: > 0 };
                });
            },
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Gets cached data for a query.
    /// </summary>
    /// <typeparam name="TResponse">The type of the cached response.</typeparam>
    /// <param name="query">The query object.</param>
    /// <param name="type">The type of the cache.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The cached data.</returns>
    public Task<TResponse?> GetCachedQueryDataAsync<TResponse>(
        ICacheableQuery query,
        CacheType? type = CacheType.Hybrid,
        CancellationToken cancellationToken = default
    )
        where TResponse : class
    {
        var cacheService = GetCachingService(type);
        var cacheKey = query.GenerateCacheKey(HttpContextAccessor);

        return cacheService.GetAsync<TResponse>(cacheKey, cancellationToken);
    }

    /// <summary>
    /// Gets cached data for a query.
    /// </summary>
    /// <typeparam name="TResponse">The type of the cached response.</typeparam>
    /// <param name="cacheKey">The cache key.</param>
    /// <param name="type">The type of the cache.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The cached data.</returns>
    public Task<TResponse?> GetCachedDataAsync<TResponse>(
        string cacheKey,
        CacheType? type = CacheType.Hybrid,
        CancellationToken cancellationToken = default
    )
    {
        var cacheService = GetCachingService(type);

        return cacheService.GetAsync<TResponse>(cacheKey, cancellationToken);
    }

    /// <summary>
    /// Sets the cache.
    /// </summary>
    /// <typeparam name="TResponse">The type of the response.</typeparam>
    /// <param name="cacheKey">The cache key.</param>
    /// <param name="value">The value.</param>
    /// <param name="expiration">The expiration.</param>
    /// <param name="type">The type of the cache.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The <see cref="Task"/> that represents the asynchronous operation.</returns>
    public Task SetCacheAsync<TResponse>(
        string cacheKey,
        TResponse value,
        TimeSpan expiration,
        CacheType? type = CacheType.Hybrid,
        CancellationToken cancellationToken = default
    )
    {
        var cacheService = GetCachingService(type);

        return cacheService.SetAsync(cacheKey, value, expiration, cancellationToken: cancellationToken);
    }

    /// <summary>
    /// Gets the scheduler instance using scoped execution.
    /// </summary>
    /// <param name="cancellationToken">The cancellation token.</param>
    public Task<IScheduler> GetSchedulerAsync(CancellationToken cancellationToken = default)
    {
        return ExecuteScopeAsync(async sp =>
        {
            var factory = sp.GetRequiredService<ISchedulerFactory>();
            var scheduler = await factory.GetScheduler(cancellationToken);
            return scheduler;
        });
    }

    /// <summary>
    /// Triggers a job immediately.
    /// </summary>
    /// <typeparam name="TJob">The type of the job to trigger.</typeparam>
    public Task TriggerJobAsync<TJob>(CancellationToken cancellationToken = default)
        where TJob : IJob
    {
        return ExecuteScopeAsync(async sp =>
        {
            var scheduler = await sp.GetRequiredService<ISchedulerFactory>().GetScheduler(cancellationToken);
            var jobKey = new JobKey(typeof(TJob).FullName!);
            await scheduler.TriggerJob(jobKey, cancellationToken);
        });
    }

    /// <summary>
    /// Gets job details.
    /// </summary>
    /// <typeparam name="TJob">The type of the job.</typeparam>
    public Task<IJobDetail?> GetJobAsync<TJob>(CancellationToken cancellationToken = default)
        where TJob : IJob
    {
        return ExecuteScopeAsync(async sp =>
        {
            var scheduler = await sp.GetRequiredService<ISchedulerFactory>().GetScheduler(cancellationToken);
            var jobKey = new JobKey(typeof(TJob).FullName!);
            return await scheduler.GetJobDetail(jobKey, cancellationToken);
        });
    }

    /// <summary>
    /// Gets triggers for a job.
    /// </summary>
    /// <typeparam name="TJob">The type of the job.</typeparam>
    public Task<IReadOnlyCollection<ITrigger>> GetJobTriggersAsync<TJob>(CancellationToken cancellationToken = default)
        where TJob : IJob
    {
        return ExecuteScopeAsync(async sp =>
        {
            var scheduler = await sp.GetRequiredService<ISchedulerFactory>().GetScheduler(cancellationToken);
            var jobKey = new JobKey(typeof(TJob).FullName!);
            return await scheduler.GetTriggersOfJob(jobKey, cancellationToken);
        });
    }

    /// <summary>
    /// Gets the next fire time for a job.
    /// </summary>
    /// <typeparam name="TJob">The type of the job.</typeparam>
    public async Task<DateTimeOffset?> GetNextFireTimeAsync<TJob>(CancellationToken cancellationToken = default)
        where TJob : IJob
    {
        var triggers = await GetJobTriggersAsync<TJob>(cancellationToken);
        return triggers.FirstOrDefault()?.GetNextFireTimeUtc();
    }

    /// <summary>
    /// Waits for a job to complete execution.
    /// </summary>
    /// <typeparam name="TJob">The type of the job.</typeparam>
    public Task WaitForJobCompletionAsync<TJob>(int? timeoutMs = null, CancellationToken cancellationToken = default)
        where TJob : IJob
    {
        return WaitUntilConditionMet(
            async () =>
            {
                var executing = await ExecuteScopeAsync(async sp =>
                {
                    var scheduler = await sp.GetRequiredService<ISchedulerFactory>().GetScheduler(cancellationToken);
                    var jobKey = new JobKey(typeof(TJob).FullName!);
                    var currentJobs = await scheduler.GetCurrentlyExecutingJobs(cancellationToken);
                    return !currentJobs.Any(x => x.JobDetail.Key.Equals(jobKey));
                });
                return executing;
            },
            timeoutMs,
            cancellationToken: cancellationToken
        );
    }

    /// <summary>
    /// Gets the caching service.
    /// </summary>
    /// <param name="type">The type of the cache.</param>
    /// <returns>The caching service.</returns>
    public ICachingService GetCachingService(CacheType? type = CacheType.Hybrid)
    {
        var cacheType = type switch
        {
            CacheType.InMemory => nameof(CacheType.InMemory),
            CacheType.Redis => nameof(CacheType.Redis),
            CacheType.Hybrid => nameof(CacheType.Hybrid),
            _ => nameof(CacheType.Hybrid),
        };

        return ServiceProvider.GetRequiredKeyedService<ICachingService>(cacheType);
    }

    /// <summary>
    /// Creates an async unary call.
    /// </summary>
    /// <typeparam name="TResponse">The type of the response.</typeparam>
    /// <param name="response">The response.</param>
    /// <returns>The async unary call.</returns>
    public AsyncUnaryCall<TResponse> CreateAsyncUnaryCall<TResponse>(TResponse response)
        where TResponse : class
    {
        return TestCalls.AsyncUnaryCall(
            Task.FromResult(response),
            Task.FromResult(new Metadata()),
            () => Status.DefaultSuccess,
            () => [],
            () => { }
        );
    }

    /// <summary>
    /// Gets the mock for the specified type.
    /// </summary>
    /// <typeparam name="TMock">The type of the mock.</typeparam>
    /// <returns>The mock.</returns>
    public TMock GetOrCreateMock<TMock>()
        where TMock : class
    {
        return (TMock)_mockServices.GetOrAdd(typeof(TMock), _ => Substitute.For<TMock>());
    }

    /// <summary>
    /// Resets all mocks.
    /// </summary>
    public void ResetMocks()
    {
        foreach (var mock in _mockServices.Values)
        {
            mock.ClearSubstitute();
        }
    }

    /// <summary>
    /// Clears the HTTP clients.
    /// </summary>
    public void ClearHttpClients()
    {
        AdminHttpClient.WithoutRequestId().WithoutOrganization();
        NormalUserHttpClient.WithoutRequestId().WithoutOrganization();
        GuestClient.WithoutRequestId().WithoutOrganization();
    }

    /// <summary>
    /// Gets the company ID.
    /// </summary>
    /// <param name="isOwner">The value indicating whether the organization is owner.</param>
    /// <returns>The company ID.</returns>
    public CompanyBranchData GetOrganizationFromRequest(bool isOwner)
    {
        var companyId = Guid.Parse(
            isOwner ? Constants.Organization.Owning.CompanyId : Constants.Organization.NotOwning.CompanyId
        );
        var branchId = Guid.Parse(
            isOwner ? Constants.Organization.Owning.BranchId : Constants.Organization.NotOwning.BranchId
        );
        return new CompanyBranchData(companyId, branchId);
    }

    /// <summary>
    /// Gets the current user.
    /// </summary>
    /// <returns>The current user.</returns>
    public async Task<Guid> GetCurrentUserIdAsync(bool e2e = false)
    {
        return e2e ? GetLoggedInUserId() : await GetUserIdentityAsync();
    }

    /// <summary>
    /// Gets the organization.
    /// </summary>
    /// <param name="isOwner">The value indicating whether the organization is owner.</param>
    /// <param name="e2e">Indicates whether test is an e2e or not.</param>
    /// <returns>The organization.</returns>
    public async Task<CompanyBranchData> GetOrganization(bool isOwner = true, bool e2e = false)
    {
        return e2e ? GetOrganizationFromRequest(isOwner) : await GetUserCompanyBranchAsync();
    }

    /// <summary>
    /// Gets the logged-in user ID.
    /// </summary>
    /// <param name="isAdmin">The value indicating whether the user is admin.</param>
    /// <returns>The logged-in user ID.</returns>
    public Guid GetLoggedInUserId(bool isAdmin = false)
    {
        var userId = isAdmin ? Constants.Users.Admin.UserId : Constants.Users.NormalUser.UserId;
        return Guid.Parse(userId);
    }

    /// <summary>
    /// Gets the logged-in user Email.
    /// </summary>
    /// <param name="isAdmin">The value indicating whether the user is admin.</param>
    /// <returns>The logged-in user FullName.</returns>
    public string GetLoggedInUserEmail(bool isAdmin = false)
    {
        return isAdmin ? Constants.Users.Admin.Email : Constants.Users.NormalUser.Email;
    }

    /// <summary>
    /// Gets the logged-in user full name.
    /// </summary>
    /// <param name="isAdmin">The value indicating whether the user is admin.</param>
    /// <returns>The logged-in user FullName.</returns>
    public string GetLoggedInUserFullName(bool isAdmin = false)
    {
        return isAdmin ? Constants.Users.Admin.UserName : Constants.Users.NormalUser.UserName;
    }

    #region Privates

    /// <summary>
    /// Creates the admin HTTP client.
    /// </summary>
    /// <returns>The HTTP client.</returns>
    private HttpClient CreateAdminHttpClient()
    {
        var adminClient = Factory.CreateClient();

        adminClient.DefaultRequestHeaders.Accept.Add(
            new MediaTypeWithQualityHeaderValue(GlobalApplicationConstValues.Header.ApplicationJsonContent)
        );

        var claims = CreateAdminUserMock().Claims;

        adminClient.SetFakeJwtBearerClaims(claims);

        return adminClient;
    }

    /// <summary>
    /// Creates the normal user HTTP client.
    /// </summary>
    /// <returns>The HTTP client.</returns>
    private HttpClient CreateNormalUserHttpClient()
    {
        var userClient = Factory.CreateClient();

        userClient.DefaultRequestHeaders.Accept.Add(
            new MediaTypeWithQualityHeaderValue(GlobalApplicationConstValues.Header.ApplicationJsonContent)
        );

        var claims = CreateNormalUserMock().Claims;

        userClient.SetFakeJwtBearerClaims(claims);

        return userClient;
    }

    /// <summary>
    /// Creates the admin user mock.
    /// </summary>
    /// <returns>The mock user.</returns>
    private MockAuthUser CreateAdminUserMock()
    {
        var roleClaim = new Claim(ClaimTypes.Role, Constants.Users.Admin.Role);
        List<Claim> otherClaims =
        [
            new(ClaimTypes.NameIdentifier, Constants.Users.Admin.UserId),
            new(ClaimTypes.Name, Constants.Users.Admin.UserName),
            new(ClaimTypes.Email, Constants.Users.Admin.Email),
        ];

        return _ = new MockAuthUser([.. otherClaims, roleClaim]);
    }

    /// <summary>
    /// Creates the normal user mock.
    /// </summary>
    /// <returns>The mock user.</returns>
    private MockAuthUser CreateNormalUserMock()
    {
        List<Claim> otherClaims =
        [
            new(ClaimTypes.NameIdentifier, Constants.Users.NormalUser.UserId),
            new(ClaimTypes.Name, Constants.Users.NormalUser.UserName),
            new(ClaimTypes.Email, Constants.Users.NormalUser.Email),
        ];

        return _ = new MockAuthUser([.. otherClaims]);
    }

    #endregion
}
