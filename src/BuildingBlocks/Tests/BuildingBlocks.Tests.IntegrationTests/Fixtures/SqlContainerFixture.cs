using BuildingBlocks.Core.IdGenerator;
using BuildingBlocks.Tests.Shared.Helpers;
using Dapper;
using Microsoft.Data.SqlClient;
using Respawn;
using Testcontainers.MsSql;
using Xunit.Sdk;

namespace BuildingBlocks.Tests.IntegrationTests.Fixtures;

/// <summary>
/// Represents a sql container fixture.
/// </summary>
public class SqlContainerFixture : IAsyncLifetime
{
    /// <summary>
    /// Gets the sql container options.
    /// </summary>
    private SqlContainerOptions SqlContainerOptions { get; }

    /// <summary>
    /// Gets the host port.
    /// </summary>
    private int HostPort => Container.GetMappedPublicPort(MsSqlBuilder.MsSqlPort);

    /// <summary>
    /// Gets the tcp container port.
    /// </summary>
    private static int TcpContainerPort => MsSqlBuilder.MsSqlPort;

    /// <summary>
    /// Gets the message sink.
    /// </summary>
    private readonly IMessageSink _messageSink;

    /// <summary>
    /// Gets the container.
    /// </summary>
    public MsSqlContainer Container { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="SqlContainerFixture"/> class.
    /// </summary>
    /// <param name="messageSink">The message sink.</param>
    public SqlContainerFixture(IMessageSink messageSink)
    {
        _messageSink = messageSink;
        SqlContainerOptions = ConfigurationHelper.BindOptions<SqlContainerOptions>();
        ArgumentNullException.ThrowIfNull(SqlContainerOptions);

        var sqlContainerBuilder = new MsSqlBuilder()
            .WithCleanUp(true)
            .WithName(SqlContainerOptions.Name)
            .WithImage(SqlContainerOptions.ImageName);

        Container = sqlContainerBuilder.Build();
    }

    /// <inheritdoc />
    public async Task InitializeAsync()
    {
        await Container.StartAsync();
        _messageSink.OnMessage(
            new DiagnosticMessage(
                $"Sql fixture started on Host port {HostPort} and container tcp port {TcpContainerPort}..."
            )
        );
    }

    /// <summary>
    /// Resets the database.
    /// </summary>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task ResetDbAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await using var connection = new SqlConnection(Container.GetConnectionString());
            await connection.OpenAsync(cancellationToken);
            // await CheckForExistingDatabase(connection);

            var checkpoint = await Respawner.CreateAsync(
                connection,
                new RespawnerOptions { DbAdapter = DbAdapter.SqlServer }
            );

            await checkpoint.ResetAsync(connection);
        }
        catch (Exception e)
        {
            _messageSink.OnMessage(new DiagnosticMessage(e.Message));
        }
    }

    /// <summary>
    /// Resets the database.
    /// </summary>
    public async Task DisposeAsync()
    {
        await Container.StopAsync();
        await Container.DisposeAsync();
        _messageSink.OnMessage(new DiagnosticMessage("Sql fixture stopped."));
    }

    /// <summary>
    /// Checks for existing database(should replace master with db name).
    /// </summary>
    /// <param name="connection">The connection.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    private async Task CheckForExistingDatabase(SqlConnection connection)
    {
        var existsDb = await connection.ExecuteScalarAsync<bool>(
            $"""
            SELECT COUNT(*) FROM sys.databases
            WHERE name = @dbname
            """,
            param: new { dbname = SqlContainerOptions.DatabaseName }
        );
        if (!existsDb)
        {
            await connection.ExecuteAsync($"CREATE DATABASE [{SqlContainerOptions.DatabaseName}]");
        }
    }
}

/// <summary>
/// Represents the sql container options.
/// </summary>
public sealed class SqlContainerOptions
{
    /// <summary>
    /// Gets or sets the name.
    /// </summary>
    public string Name { get; set; } = "sql_test_" + IdGenerator.NewId();

    /// <summary>
    /// Gets or sets the image name.
    /// </summary>
    public string ImageName { get; set; } = "mcr.microsoft.com/mssql/server:latest";

    /// <summary>
    /// Gets or sets the database name.
    /// </summary>
    public string DatabaseName { get; set; } = "test_db";
}
