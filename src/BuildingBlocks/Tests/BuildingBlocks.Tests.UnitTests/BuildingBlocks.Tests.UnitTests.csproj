<Project Sdk="Microsoft.NET.Sdk">
  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" />
  </ItemGroup>
  <PropertyGroup>
    <IsPackable>true</IsPackable>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\BuildingBlocks.EventBus.Masstransit\BuildingBlocks.EventBus.Masstransit.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks.Persistence\BuildingBlocks.Persistence.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks.Web\BuildingBlocks.Web.csproj" />
    <ProjectReference Include="..\BuildingBlocks.Tests.Shared\BuildingBlocks.Tests.Shared.csproj" />
  </ItemGroup>
</Project>
