using System.Text.Json.Serialization;
using BuildingBlocks.Core.Common.Errors;

namespace BuildingBlocks.Core.Common.Maybe;

/// <summary>
/// Represents a wrapper around a value that may or may not be null.
/// </summary>
/// <typeparam name="T">The value type.</typeparam>
public class Maybe<T> : IEquatable<Maybe<T>>
{
    /// <summary>
    /// Gets the value associated with the <see cref="Maybe{T}"/> instance, if any.
    /// </summary>
    public T? Value { get; }

    /// <summary>
    /// Gets the appError associated with the <see cref="Maybe{T}"/> instance, if any.
    /// </summary>
    public AppError? Error { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="Maybe{T}"/> class.
    /// </summary>
    /// <param name="value">The value.</param>
    /// <param name="error">The appError.</param>
    [JsonConstructor]
    public Maybe(T? value, AppError? error = null)
    {
        Value = value;
        Error = error;
    }

    /// <summary>
    /// Gets the default instance.
    /// </summary>
    public static Maybe<T> None => new(default);

    /// <summary>
    /// Gets a value indicating whether the value exists.
    /// </summary>
    [JsonIgnore]
    public bool HasValue => !HasNoValue;

    /// <summary>
    /// Gets a value indicating whether the value does not exist.
    /// </summary>
    [JsonIgnore]
    public bool HasNoValue => Value is null;

    /// <summary>
    /// Creates a new <see cref="Maybe{T}"/> instance based on the specified value.
    /// </summary>
    /// <param name="value">The value.</param>
    /// <returns>The new <see cref="Maybe{T}"/> instance.</returns>
    public static Maybe<T> From(T value) => new(value);

    /// <summary>
    /// Creates a new <see cref="Maybe{T}"/> instance based on the specified appError.
    /// </summary>
    /// <param name="error">The appError.</param>
    /// <returns>The new <see cref="Maybe{T}"/> instance.</returns>
    public static Maybe<T> FromError(AppError? error) => new(default, error);

    /// <summary>
    /// Implicitly converts a value to a 'Maybe' type representing an existing or potential value.
    /// </summary>
    /// <param name="value">The value to convert to a 'Maybe' type.</param>
    public static implicit operator Maybe<T>(T value) => From(value);

    /// <summary>
    /// Implicitly converts a 'Maybe' type to its underlying value.
    /// </summary>
    /// <param name="maybe">The 'Maybe' type to convert to its underlying value.</param>
    public static implicit operator T?(Maybe<T> maybe) => maybe.Value;

    /// <summary>
    /// Implicitly converts an appError to a 'Maybe' type representing an appError state.
    /// </summary>
    /// <param name="appError">The appError to convert to a 'Maybe' type.</param>
    public static implicit operator Maybe<T>(AppError appError) => FromError(appError);

    /// <inheritdoc />
    public bool Equals(Maybe<T>? other)
    {
        if (other is null)
        {
            return false;
        }

        if (HasNoValue && other.HasNoValue)
        {
            return (Error?.Equals(other.Error) ?? other.Error is null) && Value is null && other.Value is null;
        }

        if (HasNoValue || other.HasNoValue)
        {
            return false;
        }

        return Value!.Equals(other.Value) && (Error?.Equals(other.Error) ?? other.Error is null);
    }

    /// <inheritdoc />
    public override bool Equals(object? obj) =>
        obj switch
        {
            null => false,
            T value => Equals(new Maybe<T>(value)),
            Maybe<T> maybe => Equals(maybe),
            _ => false,
        };

    /// <inheritdoc />
    public override int GetHashCode()
    {
        var hash = default(HashCode);
        hash.Add(Value);
        hash.Add(Error);
        return hash.ToHashCode();
    }
}
