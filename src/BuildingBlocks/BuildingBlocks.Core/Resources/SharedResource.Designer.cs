//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace BuildingBlocks.Core.Resources
{
    using System;


    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class SharedResource
    {

        private static System.Resources.ResourceManager resourceMan;

        private static System.Globalization.CultureInfo resourceCulture;

        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal SharedResource()
        {
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Resources.ResourceManager ResourceManager
        {
            get
            {
                if (object.Equals(null, resourceMan))
                {
                    System.Resources.ResourceManager temp = new System.Resources.ResourceManager("BuildingBlocks.Core.Resources.SharedResource", typeof(SharedResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Globalization.CultureInfo Culture
        {
            get
            {
                return resourceCulture;
            }
            set
            {
                resourceCulture = value;
            }
        }

        public static string Access_Denied_Error
        {
            get
            {
                return ResourceManager.GetString("Access_Denied_Error", resourceCulture);
            }
        }

        public static string Access_Denied_Message
        {
            get
            {
                return ResourceManager.GetString("Access_Denied_Message", resourceCulture);
            }
        }

        public static string Application_Error
        {
            get
            {
                return ResourceManager.GetString("Application_Error", resourceCulture);
            }
        }

        public static string Area_Code
        {
            get
            {
                return ResourceManager.GetString("Area_Code", resourceCulture);
            }
        }

        public static string Authentication_Error
        {
            get
            {
                return ResourceManager.GetString("Authentication_Error", resourceCulture);
            }
        }

        public static string Business_Rule_Error
        {
            get
            {
                return ResourceManager.GetString("Business_Rule_Error", resourceCulture);
            }
        }

        public static string City
        {
            get
            {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }

        public static string Concurrency_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Concurrency_Error_Message", resourceCulture);
            }
        }

        public static string Country_Code
        {
            get
            {
                return ResourceManager.GetString("Country_Code", resourceCulture);
            }
        }

        public static string Create_Failed_Message
        {
            get
            {
                return ResourceManager.GetString("Create_Failed_Message", resourceCulture);
            }
        }

        public static string Create_Success_Message
        {
            get
            {
                return ResourceManager.GetString("Create_Success_Message", resourceCulture);
            }
        }

        public static string Database_Error
        {
            get
            {
                return ResourceManager.GetString("Database_Error", resourceCulture);
            }
        }

        public static string Data_Mismatch_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Data_Mismatch_Error_Message", resourceCulture);
            }
        }

        public static string Delete_Failed_Message
        {
            get
            {
                return ResourceManager.GetString("Delete_Failed_Message", resourceCulture);
            }
        }

        public static string Delete_Success_Message
        {
            get
            {
                return ResourceManager.GetString("Delete_Success_Message", resourceCulture);
            }
        }

        public static string Description
        {
            get
            {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }

        public static string Domain_Error
        {
            get
            {
                return ResourceManager.GetString("Domain_Error", resourceCulture);
            }
        }

        public static string Duration
        {
            get
            {
                return ResourceManager.GetString("Duration", resourceCulture);
            }
        }

        public static string Email
        {
            get
            {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }

        public static string Entity_Id
        {
            get
            {
                return ResourceManager.GetString("Entity_Id", resourceCulture);
            }
        }

        public static string Error
        {
            get
            {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }

        public static string First_Name
        {
            get
            {
                return ResourceManager.GetString("First_Name", resourceCulture);
            }
        }

        public static string Full_Address
        {
            get
            {
                return ResourceManager.GetString("Full_Address", resourceCulture);
            }
        }

        public static string Greater_Than_Or_Equal_To_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Greater_Than_Or_Equal_To_Error_Message", resourceCulture);
            }
        }

        public static string Greater_Than_Zero_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Greater_Than_Zero_Error_Message", resourceCulture);
            }
        }

        public static string Id
        {
            get
            {
                return ResourceManager.GetString("Id", resourceCulture);
            }
        }

        public static string Internal_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Internal_Error_Message", resourceCulture);
            }
        }

        public static string Invalid_Token_Message
        {
            get
            {
                return ResourceManager.GetString("Invalid_Token_Message", resourceCulture);
            }
        }

        public static string Last_Name
        {
            get
            {
                return ResourceManager.GetString("Last_Name", resourceCulture);
            }
        }

        public static string Length_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Length_Error_Message", resourceCulture);
            }
        }

        public static string Less_Than_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Less_Than_Error_Message", resourceCulture);
            }
        }

        public static string Less_Than_Or_Equal_To_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Less_Than_Or_Equal_To_Error_Message", resourceCulture);
            }
        }

        public static string Local_Number
        {
            get
            {
                return ResourceManager.GetString("Local_Number", resourceCulture);
            }
        }

        public static string Max_Length_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Max_Length_Error_Message", resourceCulture);
            }
        }

        public static string Min_Length_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Min_Length_Error_Message", resourceCulture);
            }
        }

        public static string Mobile
        {
            get
            {
                return ResourceManager.GetString("Mobile", resourceCulture);
            }
        }

        public static string Money_Currency
        {
            get
            {
                return ResourceManager.GetString("Money_Currency", resourceCulture);
            }
        }

        public static string Money_Currency_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Money_Currency_Error_Message", resourceCulture);
            }
        }

        public static string Money_Value
        {
            get
            {
                return ResourceManager.GetString("Money_Value", resourceCulture);
            }
        }

        public static string Month_Period
        {
            get
            {
                return ResourceManager.GetString("Month_Period", resourceCulture);
            }
        }

        public static string Name
        {
            get
            {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }

        public static string National_Code
        {
            get
            {
                return ResourceManager.GetString("National_Code", resourceCulture);
            }
        }

        public static string Network_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Network_Error_Message", resourceCulture);
            }
        }

        public static string Not_Found
        {
            get
            {
                return ResourceManager.GetString("Not_Found", resourceCulture);
            }
        }

        public static string Not_Found_Error
        {
            get
            {
                return ResourceManager.GetString("Not_Found_Error", resourceCulture);
            }
        }

        public static string Not_Found_Message
        {
            get
            {
                return ResourceManager.GetString("Not_Found_Message", resourceCulture);
            }
        }

        public static string Number
        {
            get
            {
                return ResourceManager.GetString("Number", resourceCulture);
            }
        }

        public static string Operation_Failed_Message
        {
            get
            {
                return ResourceManager.GetString("Operation_Failed_Message", resourceCulture);
            }
        }

        public static string Operation_Invalid_Message
        {
            get
            {
                return ResourceManager.GetString("Operation_Invalid_Message", resourceCulture);
            }
        }

        public static string Operation_Success_Message
        {
            get
            {
                return ResourceManager.GetString("Operation_Success_Message", resourceCulture);
            }
        }

        public static string Page
        {
            get
            {
                return ResourceManager.GetString("Page", resourceCulture);
            }
        }

        public static string Page_Size
        {
            get
            {
                return ResourceManager.GetString("Page_Size", resourceCulture);
            }
        }

        public static string Password
        {
            get
            {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }

        public static string Price
        {
            get
            {
                return ResourceManager.GetString("Price", resourceCulture);
            }
        }

        public static string Product
        {
            get
            {
                return ResourceManager.GetString("Product", resourceCulture);
            }
        }

        public static string Required_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Required_Error_Message", resourceCulture);
            }
        }

        public static string Server_Error
        {
            get
            {
                return ResourceManager.GetString("Server_Error", resourceCulture);
            }
        }

        public static string State
        {
            get
            {
                return ResourceManager.GetString("State", resourceCulture);
            }
        }

        public static string Sync_Data_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Sync_Data_Error_Message", resourceCulture);
            }
        }

        public static string Telephone
        {
            get
            {
                return ResourceManager.GetString("Telephone", resourceCulture);
            }
        }

        public static string Text
        {
            get
            {
                return ResourceManager.GetString("Text", resourceCulture);
            }
        }

        public static string Title
        {
            get
            {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }

        public static string Token
        {
            get
            {
                return ResourceManager.GetString("Token", resourceCulture);
            }
        }

        public static string Unknown_Error
        {
            get
            {
                return ResourceManager.GetString("Unknown_Error", resourceCulture);
            }
        }

        public static string Unknown_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Unknown_Error_Message", resourceCulture);
            }
        }

        public static string Update_Failed_Message
        {
            get
            {
                return ResourceManager.GetString("Update_Failed_Message", resourceCulture);
            }
        }

        public static string Update_Success_Message
        {
            get
            {
                return ResourceManager.GetString("Update_Success_Message", resourceCulture);
            }
        }

        public static string User
        {
            get
            {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }

        public static string Users
        {
            get
            {
                return ResourceManager.GetString("Users", resourceCulture);
            }
        }

        public static string Validation
        {
            get
            {
                return ResourceManager.GetString("Validation", resourceCulture);
            }
        }

        public static string Validation_Error
        {
            get
            {
                return ResourceManager.GetString("Validation_Error", resourceCulture);
            }
        }

        public static string Validation_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Validation_Error_Message", resourceCulture);
            }
        }

        public static string Zip_Code
        {
            get
            {
                return ResourceManager.GetString("Zip_Code", resourceCulture);
            }
        }

        public static string Code
        {
            get
            {
                return ResourceManager.GetString("Code", resourceCulture);
            }
        }

        public static string Data_Not_Valid_Error
        {
            get
            {
                return ResourceManager.GetString("Data_Not_Valid_Error", resourceCulture);
            }
        }

        public static string Request
        {
            get
            {
                return ResourceManager.GetString("Request", resourceCulture);
            }
        }

        public static string Person
        {
            get
            {
                return ResourceManager.GetString("Person", resourceCulture);
            }
        }

        public static string Author
        {
            get
            {
                return ResourceManager.GetString("Author", resourceCulture);
            }
        }

        public static string Value
        {
            get
            {
                return ResourceManager.GetString("Value", resourceCulture);
            }
        }

        public static string Address
        {
            get
            {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }

        public static string Invalid_Data_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Invalid_Data_Error_Message", resourceCulture);
            }
        }

        public static string Date_Prop
        {
            get
            {
                return ResourceManager.GetString("Date_Prop", resourceCulture);
            }
        }

        public static string Start
        {
            get
            {
                return ResourceManager.GetString("Start", resourceCulture);
            }
        }

        public static string End
        {
            get
            {
                return ResourceManager.GetString("End", resourceCulture);
            }
        }

        public static string Name_Prop
        {
            get
            {
                return ResourceManager.GetString("Name_Prop", resourceCulture);
            }
        }

        public static string Number_Only_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Number_Only_Error_Message", resourceCulture);
            }
        }

        public static string Country
        {
            get
            {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }

        public static string Province
        {
            get
            {
                return ResourceManager.GetString("Province", resourceCulture);
            }
        }

        public static string Street
        {
            get
            {
                return ResourceManager.GetString("Street", resourceCulture);
            }
        }

        public static string Apartment_Or_Suite
        {
            get
            {
                return ResourceManager.GetString("Apartment_Or_Suite", resourceCulture);
            }
        }

        public static string Latitude
        {
            get
            {
                return ResourceManager.GetString("Latitude", resourceCulture);
            }
        }

        public static string Longitude
        {
            get
            {
                return ResourceManager.GetString("Longitude", resourceCulture);
            }
        }

        public static string County
        {
            get
            {
                return ResourceManager.GetString("County", resourceCulture);
            }
        }

        public static string Region
        {
            get
            {
                return ResourceManager.GetString("Region", resourceCulture);
            }
        }

        public static string Zone
        {
            get
            {
                return ResourceManager.GetString("Zone", resourceCulture);
            }
        }

        public static string Neighborhood
        {
            get
            {
                return ResourceManager.GetString("Neighborhood", resourceCulture);
            }
        }

        public static string Link
        {
            get
            {
                return ResourceManager.GetString("Link", resourceCulture);
            }
        }

        public static string Type
        {
            get
            {
                return ResourceManager.GetString("Type", resourceCulture);
            }
        }

        public static string Invalid_Date_Error_Message
        {
            get
            {
                return ResourceManager.GetString("Invalid_Date_Error_Message", resourceCulture);
            }
        }

        public static string Free_Trial
        {
            get
            {
                return ResourceManager.GetString("Free_Trial", resourceCulture);
            }
        }
    }
}
