using Microsoft.Extensions.DependencyInjection;

namespace BuildingBlocks.Core.Serialization;

/// <summary>
/// Represents the serialization extensions.
/// </summary>
public static class SerializationExtensions
{
    /// <summary>
    /// Adds the message serialization.
    /// </summary>
    /// <param name="services">The services.</param>
    /// <returns>The service collection.</returns>
    public static IServiceCollection AddDefaultSerialization(this IServiceCollection services)
    {
        services.AddSingleton<ISerializer, DefaultSerializer>();

        return services;
    }
}
