using Bogus;
using MainArchitecture.TestShared.Fakes.Common.Requests;
using Organization.GrpcServices;

namespace Web.Bff.Aggregator.TestShared.Fakes.Organization.Requests;

/// <summary>
/// Represents a fake get geographical areas gRPC response list.
/// </summary>
public sealed class FakeGetGeographicalAreasGrpcResponseList : Faker<GetGeographicalAreasGrpcResponseList>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeGetGeographicalAreasGrpcResponseList"/> class.
    /// </summary>
    public FakeGetGeographicalAreasGrpcResponseList()
    {
        CustomInstantiator(_ => new GetGeographicalAreasGrpcResponseList
        {
            Data = { new FakeOptionValue().Generate(3) },
        });
    }
}
