using AutoBogus;
using Web.Bff.Aggregator.Features.WarehouseAndInventory.Product.Queries.GetDataForProductPriceOperation;

namespace Web.Bff.Aggregator.TestShared.Fakes.Warehouse.Requests;

/// <summary>
/// Fake response for getting data for product price operation.
/// </summary>
public sealed class FakeGetDataForProductPriceOperationQueryResponse
    : AutoFaker<GetDataForProductPriceOperationQueryResponse>;
