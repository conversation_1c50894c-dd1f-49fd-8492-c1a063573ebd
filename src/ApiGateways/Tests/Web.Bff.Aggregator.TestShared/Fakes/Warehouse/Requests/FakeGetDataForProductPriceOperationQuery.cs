using AutoBogus;
using MainArchitecture.TestShared.Helpers;
using Web.Bff.Aggregator.Features.WarehouseAndInventory.Product.Queries.GetDataForProductPriceOperation;

namespace Web.Bff.Aggregator.TestShared.Fakes.Warehouse.Requests;

/// <summary>
/// Fake query for getting data for product price operation.
/// </summary>
public sealed class FakeGetDataForProductPriceOperationQuery : AutoFaker<GetDataForProductPriceOperationQuery>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FakeGetDataForProductPriceOperationQuery"/> class.
    /// </summary>
    public FakeGetDataForProductPriceOperationQuery(bool isCacheEnabled = false)
    {
        if (isCacheEnabled)
        {
            this.ApplyCache();
        }
        else
        {
            this.ApplyNoCache();
        }
    }
}
