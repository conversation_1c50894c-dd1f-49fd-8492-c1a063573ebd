using BuildingBlocks.Core.Common.Maybe;
using MainArchitecture.Application.Models.Common;
using Web.Bff.Aggregator.Features.Account.Common.Models.Vm;
using Web.Bff.Aggregator.Features.Account.Role.Models.Vm;

namespace Web.Bff.Aggregator.Features.Account.Services.Contracts;

/// <summary>
/// Account Services interface.
/// </summary>
public interface IAccountService
{
    #region System

    /// <summary>
    /// Get System Parts.
    /// </summary>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>system parts.</returns>
    Task<Maybe<IReadOnlyCollection<SystemPartVm>>> GetSystemParts(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get Access Levels.
    /// </summary>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>access levels.</returns>
    Task<Maybe<IReadOnlyCollection<NumericOptionVm>>> GetAccessLevels(CancellationToken cancellationToken = default);

    #endregion

    #region Role

    /// <summary>
    /// Get roles with permissions.
    /// </summary>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>roles with permissions.</returns>
    Task<Maybe<IReadOnlyCollection<RoleWithPermissionVm>>> GetRolesWithPermissions(
        CancellationToken cancellationToken = default
    );

    #endregion
}
