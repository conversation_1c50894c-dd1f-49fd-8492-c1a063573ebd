using AutoMapper;
using BuildingBlocks.Core.Common.Maybe;
using Google.Protobuf.WellKnownTypes;
using MainArchitecture.API.Configurations.SharedGrpcContracts;
using MainArchitecture.Application.Models.Common;
using Organization.GrpcServices;
using Web.Bff.Aggregator.Features.Organization.Company.Models.Vm;
using Web.Bff.Aggregator.Features.Organization.Services.Contracts;

namespace Web.Bff.Aggregator.Features.Organization.Services.Implementations;

/// <summary>
/// Organization Service implementation.
/// </summary>
/// <remarks>
/// Constructor to initialize class <see cref="OrganizationService"/>.
/// </remarks>
/// <param name="companyClient">The gRPC client for company-related services.</param>
/// <param name="branchClient">The gRPC client for branch-related services.</param>
/// <param name="geographicalAreaClient">The gRPC client for geographical area-related services.</param>
/// <param name="mapper">An instance of AutoMapper for mapping objects.</param>
internal sealed class OrganizationService(
    CompanyGrpcService.CompanyGrpcServiceClient companyClient,
    BranchGrpcService.BranchGrpcServiceClient branchClient,
    GeographicalAreaGrpcService.GeographicalAreaGrpcServiceClient geographicalAreaClient,
    IMapper mapper
) : IOrganizationService
{
    #region Company

    /// <inheritdoc />
    public async Task<Maybe<IReadOnlyCollection<OptionVm>>> GetCompanies(CancellationToken cancellationToken = default)
    {
        var response = await companyClient.GetCompaniesGrpcServiceAsync(
            new Empty(),
            cancellationToken: cancellationToken
        );

        return mapper.Map<List<OptionVm>>(response.Data);
    }

    /// <inheritdoc />
    public async Task<Maybe<IReadOnlyCollection<AccessibleCompanyWithBranchVm>>> GetAccessibleCompaniesWithBranches(
        CancellationToken cancellationToken = default
    )
    {
        var response = await companyClient.GetAccessibleCompaniesWithBranchesGrpcServiceAsync(
            new Empty(),
            cancellationToken: cancellationToken
        );

        return mapper.Map<List<AccessibleCompanyWithBranchVm>>(response.Data);
    }

    #endregion

    #region Branch

    /// <inheritdoc />
    public async Task<Maybe<IReadOnlyCollection<OptionVm>>> GetBranchesBasedOnAccess(
        Guid companyId,
        CancellationToken cancellationToken = default
    )
    {
        var request = new GetBranchesBasedOnAccessRequest { CompanyId = (GuidValue)companyId };
        var response = await branchClient.GetBranchesBasedOnAccessGrpcServiceAsync(
            request,
            cancellationToken: cancellationToken
        );

        return mapper.Map<List<OptionVm>>(response.Data);
    }

    #endregion

    #region Geographical Area

    /// <inheritdoc />
    public async Task<Maybe<IReadOnlyCollection<OptionVm>>> GetGeographicalAreas(
        int typeId,
        Guid? linkId,
        CancellationToken cancellationToken = default
    )
    {
        var response = await geographicalAreaClient.GetGeographicalAreasGrpcServiceAsync(
            new GetGeographicalAreasGrpcRequest { TypeId = typeId, LinkId = (NullableGuidValue)linkId },
            cancellationToken: cancellationToken
        );

        return mapper.Map<List<OptionVm>>(response.Data);
    }

    #endregion
}
